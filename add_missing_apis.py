#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将遗漏的接口添加到 apitest-code.mdc 中
"""

import re

def read_missing_apis():
    """读取遗漏的API列表"""
    missing_apis = []
    with open("missing_apis.txt", "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:
                # 解析格式: - [ ] **X.X** 接口名称 `METHOD /api/path`
                match = re.match(r'- \[ \] \*\*(\d+\.\d+)\*\* (.+?) `(GET|POST|PUT|DELETE) (/api/[^\s`]+)`', line)
                if match:
                    api_num, name, method, path = match.groups()
                    missing_apis.append({
                        'api_num': api_num,
                        'name': name,
                        'method': method,
                        'path': path,
                        'line': line
                    })
    return missing_apis

def read_apitest_code():
    """读取 apitest-code.mdc 内容"""
    with open("apitest-code.mdc", "r", encoding="utf-8") as f:
        return f.read()

def find_insertion_points(content, missing_apis):
    """找到每个遗漏API的插入位置"""
    lines = content.split('\n')
    insertions = []
    
    for api in missing_apis:
        api_num = api['api_num']
        major, minor = api_num.split('.')
        major = int(major)
        minor = int(minor)
        
        # 找到对应的Controller section
        controller_pattern = f"### **.*Controller.*{major}个接口"
        controller_found = False
        insert_line = -1
        
        for i, line in enumerate(lines):
            # 找到对应的Controller
            if re.search(rf"### \*\*.*Controller.*\({major}个接口\)", line):
                controller_found = True
                continue
            
            if controller_found:
                # 在Controller内找到合适的插入位置
                api_match = re.match(r'- \[ \] \*\*(\d+\.\d+)\*\*', line)
                if api_match:
                    existing_api_num = api_match.group(1)
                    existing_major, existing_minor = existing_api_num.split('.')
                    existing_major = int(existing_major)
                    existing_minor = int(existing_minor)
                    
                    # 如果是不同的Controller，停止搜索
                    if existing_major != major:
                        break
                    
                    # 如果找到了应该插入的位置
                    if existing_minor > minor:
                        insert_line = i
                        break
                
                # 如果遇到下一个Controller或空行，在这里插入
                elif line.startswith('### **') or (line.strip() == '' and i > 0 and lines[i-1].strip() != ''):
                    insert_line = i
                    break
        
        # 如果没找到合适位置，添加到文件末尾
        if insert_line == -1:
            insert_line = len(lines)
        
        insertions.append({
            'api': api,
            'insert_line': insert_line
        })
    
    return insertions

def generate_api_entry(api):
    """生成API条目的完整格式"""
    return f"""- [ ] **{api['api_num']}** {api['name']} `{api['method']} {api['path']}`
  - 请求参数：待补充
  - 成功响应：`200` - 操作成功
  - 错误响应：`401` - 未登录"""

def add_missing_apis():
    """将遗漏的API添加到文件中"""
    print("读取遗漏的API列表...")
    missing_apis = read_missing_apis()
    print(f"找到 {len(missing_apis)} 个遗漏的API")
    
    print("读取 apitest-code.mdc...")
    content = read_apitest_code()
    
    print("分析插入位置...")
    insertions = find_insertion_points(content, missing_apis)
    
    # 按插入行号倒序排序，避免插入时行号变化
    insertions.sort(key=lambda x: x['insert_line'], reverse=True)
    
    lines = content.split('\n')
    
    print("插入遗漏的API...")
    for insertion in insertions:
        api = insertion['api']
        insert_line = insertion['insert_line']
        
        # 生成API条目
        api_entry = generate_api_entry(api)
        api_lines = api_entry.split('\n')
        
        # 插入API条目
        for j, api_line in enumerate(reversed(api_lines)):
            lines.insert(insert_line, api_line)
        
        print(f"插入 {api['api_num']} {api['name']} 到第 {insert_line} 行")
    
    # 写回文件
    new_content = '\n'.join(lines)
    
    # 备份原文件
    with open("apitest-code.mdc.backup", "w", encoding="utf-8") as f:
        f.write(content)
    
    # 写入新内容
    with open("apitest-code.mdc", "w", encoding="utf-8") as f:
        f.write(new_content)
    
    print(f"完成！已将 {len(missing_apis)} 个遗漏的API添加到 apitest-code.mdc")
    print("原文件已备份为 apitest-code.mdc.backup")

if __name__ == "__main__":
    add_missing_apis()
