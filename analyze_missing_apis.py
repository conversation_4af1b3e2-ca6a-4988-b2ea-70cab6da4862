#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 apitest-code.mdc 中定义的API接口与控制器中实际实现的差异
"""

import re
import os
from pathlib import Path

def extract_apis_from_apitest_code():
    """从 apitest-code.mdc 文件中提取所有API接口"""
    apis = []

    with open('apitest-code.mdc', 'r', encoding='utf-8') as f:
        content = f.read()

    # 匹配格式: - [ ] **X.X** 接口名称 `METHOD /api/path`
    pattern = r'- \[ \] \*\*(\d+\.\d+)\*\* (.+?) `(GET|POST|PUT|DELETE|PATCH) (/api/[^\s`]+)`'
    matches = re.findall(pattern, content)

    for api_num, name, method, path in matches:
        apis.append(f"{method} {path}")

    return apis

def extract_routes_from_controller(controller_path):
    """从控制器文件中提取路由信息"""
    routes = []
    
    if not os.path.exists(controller_path):
        return routes
    
    with open(controller_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 匹配@ApiRoute注释
    route_pattern = r'@ApiRoute\s*\(([^)]+)\)'
    method_pattern = r'@ApiMethod\s*\(([^)]+)\)'
    
    # 找到所有@ApiRoute和@ApiMethod的匹配
    route_matches = re.findall(route_pattern, content)
    method_matches = re.findall(method_pattern, content)
    
    # 简单处理，假设顺序对应
    for i, route in enumerate(route_matches):
        method = method_matches[i] if i < len(method_matches) else 'GET'
        # 清理路由字符串
        route = route.strip().strip('"').strip("'").strip('/')
        method = method.strip().strip('"').strip("'")
        
        if route.startswith('api/') or route.startswith('/api/'):
            if not route.startswith('/'):
                route = '/' + route
            routes.append(f"{method} {route}")
    
    return routes

def get_controller_files():
    """获取所有控制器文件"""
    controller_dir = Path('php/api/app/Http/Controllers/Api')
    if not controller_dir.exists():
        return []
    
    return list(controller_dir.glob('*.php'))

def get_controller_keywords(controller_base_name):
    """获取控制器相关的关键词"""
    keywords_map = {
        'ad': ['ad'],
        'aimodel': ['ai-model', 'ai_model'],
        'asset': ['asset'],
        'auth': ['register', 'login', 'logout', 'refresh', 'forgot-password', 'reset-password', 'verify'],
        'captcha': ['captcha'],
        'cache': ['cache'],
        'websocket': ['websocket'],
        'systemmonitor': ['system'],
        'aitask': ['task'],
        'style': ['style'],
        'applicationmonitor': ['app-monitor', 'monitor'],
        'version': ['version', 'resource'],
        'analytics': ['analytics'],
        'character': ['character'],
        'config': ['config'],
        'credits': ['credits'],
        'points': ['points'],
        'notification': ['notification'],
        'permission': ['permission'],
        'user': ['user'],
        'template': ['template'],
        'aigenerationcontroller': ['ai/generate', 'ai/tasks'],
        'audio': ['audio'],
        'batch': ['batch'],
        'dataexport': ['export'],
        'download': ['download'],
        'file': ['file'],
        'image': ['image'],
        'log': ['log'],
        'music': ['music'],
        'project': ['project'],
        'projectmanagement': ['project-management'],
        'publication': ['publication'],
        'recommendation': ['recommendation'],
        'resource': ['resource'],
        'review': ['review'],
        'social': ['social'],
        'sound': ['sound'],
        'story': ['story'],
        'video': ['video'],
        'voice': ['voice'],
        'workpublish': ['work'],
        'workflow': ['workflow'],
        'usergrowth': ['user-growth']
    }
    
    return keywords_map.get(controller_base_name, [controller_base_name])

def analyze_controller_apis(controller_file, all_apis_from_doc):
    """分析单个控制器的API实现情况"""
    controller_name = controller_file.stem
    
    # 提取控制器中的路由
    controller_routes = extract_routes_from_controller(controller_file)
    
    # 找到与该控制器相关的文档API
    controller_base_name = controller_name.lower().replace('controller', '')
    related_apis = []
    
    for api in all_apis_from_doc:
        api_lower = api.lower()
        # 匹配逻辑：检查API路径是否包含控制器相关的关键词
        keywords = get_controller_keywords(controller_base_name)
        if (controller_base_name in api_lower or 
            any(keyword in api_lower for keyword in keywords)):
            related_apis.append(api)
    
    # 找出缺失的API
    missing_apis = []
    for api in related_apis:
        if not is_api_implemented(api, controller_routes):
            missing_apis.append(api)
    
    return {
        'controller_name': controller_name,
        'controller_routes': controller_routes,
        'related_apis': related_apis,
        'missing_apis': missing_apis
    }

def is_api_implemented(api, controller_routes):
    """检查API是否已实现"""
    # 简单的匹配逻辑
    for route in controller_routes:
        if api.strip() == route.strip():
            return True
    return False

def main():
    # 提取apitest-code.mdc中的所有API
    apis_from_doc = extract_apis_from_apitest_code()
    print(f"从 apitest-code.mdc 中提取到 {len(apis_from_doc)} 个API接口")
    
    # 获取所有控制器文件
    controller_files = get_controller_files()
    print(f"找到 {len(controller_files)} 个控制器文件")
    
    # 分析每个控制器
    all_missing_apis = []
    analysis_results = []
    
    for controller_file in controller_files:
        result = analyze_controller_apis(controller_file, apis_from_doc)
        analysis_results.append(result)
        all_missing_apis.extend(result['missing_apis'])
        
        print(f"\n分析控制器: {result['controller_name']}")
        print(f"  实现的路由: {len(result['controller_routes'])}")
        print(f"  相关API: {len(result['related_apis'])}")
        print(f"  缺失API: {len(result['missing_apis'])}")
        
        if result['missing_apis']:
            print("  缺失的API:")
            for api in result['missing_apis']:
                print(f"    - {api}")
    
    # 输出结果到文件
    with open('missing_apis_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("API接口缺失分析报告\n")
        f.write("="*50 + "\n\n")
        
        f.write(f"总体统计:\n")
        f.write(f"- 文档中定义的API总数: {len(apis_from_doc)}\n")
        f.write(f"- 控制器文件总数: {len(controller_files)}\n")
        f.write(f"- 缺失的API总数: {len(all_missing_apis)}\n\n")
        
        f.write("详细分析:\n")
        f.write("-"*30 + "\n")
        
        for result in analysis_results:
            if result['missing_apis']:  # 只显示有缺失API的控制器
                f.write(f"\n{result['controller_name']}:\n")
                f.write(f"  缺失的API ({len(result['missing_apis'])}):\n")
                for api in result['missing_apis']:
                    f.write(f"    ✗ {api}\n")
        
        f.write(f"\n\n所有缺失的API汇总:\n")
        f.write("-"*30 + "\n")
        for api in sorted(set(all_missing_apis)):
            f.write(f"✗ {api}\n")

if __name__ == "__main__":
    main()
