<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\DataExportService;

use Illuminate\Http\Request;

/**
 * 统一导出控制器 - 整合数据导出与通用导出功能
 */
class DataExportController extends Controller
{
    protected $dataExportService;
    protected $authService;

    public function __construct(
        DataExportService $dataExportService,
        AuthService $authService
    ) {
        $this->dataExportService = $dataExportService;
        $this->authService = $authService;
    }

    /**
     * @ApiTitle (创建数据导出)
     * @ApiSummary (创建数据导出任务)
     * @ApiMethod (POST)
     * @ApiRoute (/api/exports/create)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="export_type", type="string", required=true, description="导出类型")
     * @ApiParams (name="export_format", type="string", required=true, description="导出格式")
     * @ApiParams (name="export_params", type="object", required=false, description="导出参数")
     * @ApiParams (name="export_filters", type="object", required=false, description="导出筛选条件")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="导出任务信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "导出任务创建成功",
     *   "data": {
     *     "export_id": 123,
     *     "export_type": "user_data",
     *     "export_format": "csv",
     *     "status": "pending",
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function createExport(Request $request)
    {
        $rules = [
            'export_type' => 'required|string|in:user_data,projects,ai_tasks,characters,points_history,files',
            'export_format' => 'required|string|in:csv,excel,json,pdf,zip',
            'export_params' => 'sometimes|array',
            'export_filters' => 'sometimes|array'
        ];

        $messages = [
            'export_type.required' => '导出类型不能为空',
            'export_type.in' => '导出类型无效',
            'export_format.required' => '导出格式不能为空',
            'export_format.in' => '导出格式无效'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->createExport(
            $user->id,
            $request->export_type,
            $request->export_format,
            $request->export_params ?? [],
            $request->export_filters ?? []
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (导出任务列表)
     * @ApiSummary (获取用户的导出任务列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/exports/list)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="export_type", type="string", required=false, description="导出类型筛选")
     * @ApiParams (name="status", type="string", required=false, description="状态筛选")
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="导出任务列表")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "exports": [
     *       {
     *         "id": 123,
     *         "export_type": "user_data",
     *         "export_format": "csv",
     *         "status": "completed",
     *         "file_size": 1024000,
     *         "human_file_size": "1.00 MB",
     *         "record_count": 100,
     *         "progress_percentage": 100,
     *         "created_at": "2024-01-01 12:00:00",
     *         "completed_at": "2024-01-01 12:01:00",
     *         "expires_at": "2024-01-08 12:01:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 10,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getExports(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $filters = [
            'export_type' => $request->get('export_type'),
            'status' => $request->get('status')
        ];

        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 20), 100);

        $result = $this->dataExportService->getUserExports($user->id, $filters, $page, $perPage);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (导出任务状态)
     * @ApiSummary (获取导出任务状态和进度)
     * @ApiMethod (GET)
     * @ApiRoute (/api/exports/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务状态")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "export_type": "user_data",
     *     "status": "processing",
     *     "progress": {
     *       "processed": 50,
     *       "total": 100,
     *       "percentage": 50.0,
     *       "message": "正在导出用户数据..."
     *     },
     *     "created_at": "2024-01-01 12:00:00",
     *     "started_at": "2024-01-01 12:00:05"
     *   }
     * })
     */
    public function getExportStatus(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->getExportStatus($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (下载导出文件)
     * @ApiSummary (下载已完成的导出文件)
     * @ApiMethod (GET)
     * @ApiRoute (/api/exports/{id}/download)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="下载信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "download_url": "https://example.com/storage/exports/export_20240101_123456.csv",
     *     "filename": "user_data_export.csv",
     *     "file_size": 1024000,
     *     "expires_at": "2024-01-08 12:01:00"
     *   }
     * })
     */
    public function downloadExport(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->downloadExport($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    // ========== 通用导出功能（整合自GeneralExportController）==========

    /**
     * @ApiTitle (创建通用导出任务)
     * @ApiSummary (创建资源导出任务)
     * @ApiMethod (POST)
     * @ApiRoute (/api/general-exports/create)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="resource_ids", type="array", required=true, description="资源ID数组")
     * @ApiParams (name="export_format", type="string", required=true, description="导出格式：zip/pdf/json/xml/csv")
     * @ApiParams (name="export_options", type="object", required=false, description="导出选项")
     * @ApiParams (name="include_metadata", type="boolean", required=false, description="是否包含元数据")
     * @ApiParams (name="include_versions", type="boolean", required=false, description="是否包含版本历史")
     * @ApiParams (name="compression_level", type="string", required=false, description="压缩级别：none/low/medium/high")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "导出任务创建成功",
     *   "data": {
     *     "export_id": 123,
     *     "status": "pending",
     *     "resource_count": 3,
     *     "export_format": "zip",
     *     "estimated_size": "15.2MB",
     *     "estimated_duration": 60,
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function createGeneralExport(Request $request)
    {
        $rules = [
            'resource_ids' => 'required|array|min:1|max:50',
            'resource_ids.*' => 'integer|exists:resources,id',
            'export_format' => 'required|string|in:zip,pdf,json,xml,csv',
            'export_options' => 'sometimes|array',
            'include_metadata' => 'sometimes|boolean',
            'include_versions' => 'sometimes|boolean',
            'compression_level' => 'sometimes|string|in:none,low,medium,high'
        ];

        $messages = [
            'resource_ids.required' => '资源ID数组不能为空',
            'resource_ids.min' => '至少需要选择1个资源',
            'resource_ids.max' => '最多支持50个资源',
            'resource_ids.*.exists' => '资源不存在',
            'export_format.required' => '导出格式不能为空',
            'export_format.in' => '导出格式必须是：zip、pdf、json、xml、csv之一',
            'compression_level.in' => '压缩级别必须是：none、low、medium、high之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $exportParams = [
            'resource_ids' => $request->resource_ids,
            'export_format' => $request->export_format,
            'export_options' => $request->get('export_options', []),
            'include_metadata' => $request->get('include_metadata', true),
            'include_versions' => $request->get('include_versions', false),
            'compression_level' => $request->get('compression_level', 'medium')
        ];

        $result = $this->dataExportService->createExportTask($user->id, $exportParams);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (获取通用导出状态)
     * @ApiSummary (查询导出任务的状态和进度)
     * @ApiMethod (GET)
     * @ApiRoute (/api/general-exports/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "export_id": 123,
     *     "status": "completed",
     *     "progress": 100,
     *     "resource_count": 3,
     *     "processed_count": 3,
     *     "export_format": "zip",
     *     "file_size": "15.2MB",
     *     "download_url": "https://aiapi.tiptop.cn/exports/download/123",
     *     "expires_at": "2024-01-08 12:00:00",
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:01:00"
     *   }
     * })
     */
    public function getGeneralExportStatus(Request $request, $exportId)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->getGeneralExportStatus($exportId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (下载通用导出文件)
     * @ApiSummary (下载已完成的导出文件)
     * @ApiMethod (GET)
     * @ApiRoute (/api/general-exports/{id}/download)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn (文件下载响应)
     */
    public function downloadGeneralExport(Request $request, $exportId)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->downloadGeneralExport($exportId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (通用导出任务列表)
     * @ApiSummary (获取用户的导出任务列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/general-exports/list)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="status", type="string", required=false, description="状态过滤")
     * @ApiParams (name="export_format", type="string", required=false, description="格式过滤")
     * @ApiParams (name="page", type="int", required=false, description="页码")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "exports": [
     *       {
     *         "export_id": 123,
     *         "status": "completed",
     *         "resource_count": 3,
     *         "export_format": "zip",
     *         "file_size": "15.2MB",
     *         "download_count": 2,
     *         "created_at": "2024-01-01 12:00:00",
     *         "expires_at": "2024-01-08 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 10,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function getGeneralExportList(Request $request)
    {
        $rules = [
            'status' => 'sometimes|string|in:pending,processing,completed,failed',
            'export_format' => 'sometimes|string|in:zip,pdf,json,xml,csv',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $filters = [
            'status' => $request->get('status'),
            'export_format' => $request->get('export_format'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->dataExportService->getGeneralExportList($user->id, $filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (取消通用导出任务)
     * @ApiSummary (取消正在进行的导出任务)
     * @ApiMethod (POST)
     * @ApiRoute (/api/general-exports/{id}/cancel)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "导出任务取消成功",
     *   "data": {
     *     "export_id": 123,
     *     "status": "cancelled",
     *     "cancelled_at": "2024-01-01 12:00:30"
     *   }
     * })
     */
    public function cancelGeneralExport(Request $request, $exportId)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->cancelGeneralExport($exportId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (删除通用导出任务)
     * @ApiSummary (删除导出任务及其文件)
     * @ApiMethod (DELETE)
     * @ApiRoute (/api/general-exports/{id})
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="导出任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "导出任务删除成功",
     *   "data": {
     *     "export_id": 123,
     *     "deleted_files": 1,
     *     "freed_space": "15.2MB"
     *   }
     * })
     */
    public function deleteGeneralExport(Request $request, $exportId)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->deleteGeneralExport($exportId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (批量通用导出)
     * @ApiSummary (批量创建多个导出任务)
     * @ApiMethod (POST)
     * @ApiRoute (/api/general-exports/batch)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="export_configs", type="array", required=true, description="导出配置数组")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "批量导出任务创建成功",
     *   "data": {
     *     "batch_id": "batch_123",
     *     "export_ids": [123, 124, 125],
     *     "total_count": 3,
     *     "estimated_total_size": "45.6MB"
     *   }
     * })
     */
    public function batchCreateGeneralExport(Request $request)
    {
        $rules = [
            'export_configs' => 'required|array|min:1|max:10',
            'export_configs.*.resource_ids' => 'required|array|min:1',
            'export_configs.*.resource_ids.*' => 'integer|exists:resources,id',
            'export_configs.*.export_format' => 'required|string|in:zip,pdf,json,xml,csv'
        ];

        $messages = [
            'export_configs.required' => '导出配置数组不能为空',
            'export_configs.min' => '至少需要1个导出配置',
            'export_configs.max' => '最多支持10个导出配置',
            'export_configs.*.resource_ids.required' => '资源ID数组不能为空',
            'export_configs.*.export_format.required' => '导出格式不能为空'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->dataExportService->batchCreateGeneralExports($user->id, $request->export_configs);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
