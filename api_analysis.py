#!/usr/bin/env python3
"""
API接口差异化分析脚本
对比 apitest-code.mdc 文档中的API接口与实际路由定义的差异
"""

import re
from typing import List, Set, <PERSON><PERSON>

def extract_doc_apis() -> List[Tuple[int, str, str]]:
    """从文档中提取API接口"""
    doc_apis = []
    
    # 从搜索结果中提取的文档API接口
    doc_content = [
        (6, "POST", "/api/ad/store"),
        (10, "POST", "/api/ad/update"),
        (16, "GET", "/api/ai-models/available"),
        (20, "GET", "/api/ai-models/{model_id}/detail"),
        (24, "GET", "/api/ai-models/favorites"),
        (28, "GET", "/api/ai-models/list"),
        (32, "POST", "/api/ai-models/switch"),
        (36, "GET", "/api/ai-models/platform-comparison"),
        (40, "GET", "/api/ai-models/business-platforms"),
        (46, "GET", "/api/assets/list"),
        (50, "GET", "/api/assets/{id}"),
        (54, "DELETE", "/api/assets/{id}"),
        (60, "POST", "/api/register"),
        (64, "POST", "/api/login"),
        (68, "POST", "/api/logout"),
        (72, "POST", "/api/refresh"),
        (76, "POST", "/api/forgot-password"),
        (80, "POST", "/api/reset-password"),
        (84, "GET", "/api/verify"),
        (90, "GET", "/api/captcha/generate"),
        (94, "POST", "/api/captcha/verify"),
        (98, "POST", "/api/captcha/refresh"),
        (104, "GET", "/api/cache/stats"),
        (108, "GET", "/api/cache/keys"),
        (112, "GET", "/api/cache/get"),
        (116, "GET", "/api/cache/config"),
        (122, "POST", "/api/websocket/auth"),
        (126, "GET", "/api/websocket/sessions"),
        (130, "POST", "/api/websocket/disconnect"),
        (134, "GET", "/api/websocket/status"),
        (140, "GET", "/api/system/health"),
        (144, "GET", "/api/system/metrics"),
        (148, "GET", "/api/system/response-time"),
        (152, "GET", "/api/system/monitor/overview"),
        (156, "GET", "/api/system/monitor/metrics"),
        (160, "GET", "/api/system/search"),
        (166, "POST", "/api/tasks"),
        (170, "GET", "/api/tasks"),
        (174, "GET", "/api/tasks/{id}"),
        (178, "GET", "/api/tasks/stats"),
        (182, "GET", "/api/tasks/timeout-config"),
        (186, "POST", "/api/tasks/{id}/cancel"),
        (190, "POST", "/api/tasks/{id}/retry"),
        (194, "GET", "/api/tasks/batch/status"),
        (198, "GET", "/api/tasks/{id}/recovery"),
        (204, "GET", "/api/styles/list"),
        (208, "GET", "/api/styles/{id}"),
        (212, "GET", "/api/styles/popular"),
        (216, "POST", "/api/styles/create"),
        (222, "GET", "/api/app-monitor/health"),
        (226, "GET", "/api/monitor/metrics"),
        (230, "GET", "/api/monitor/realtime"),
        (234, "GET", "/api/monitor/alerts"),
        (238, "PUT", "/api/monitor/alerts/{id}/acknowledge"),
        (242, "PUT", "/api/monitor/alerts/{id}/resolve"),
        (248, "POST", "/api/resources/{id}/versions"),
        (252, "GET", "/api/resources/{id}/versions"),
        (256, "GET", "/api/versions/{id}"),
        (260, "PUT", "/api/versions/{id}/set-current"),
        (264, "DELETE", "/api/versions/{id}"),
        (268, "GET", "/api/versions/compare"),
        (278, "POST", "/api/ai-models/{model_id}/test"),
        (282, "GET", "/api/ai-models/usage-stats"),
        (286, "POST", "/api/ai-models/{model_id}/favorite"),
        (295, "GET", "/api/analytics/user-behavior"),
        (299, "GET", "/api/analytics/system-usage"),
        (303, "GET", "/api/analytics/ai-performance"),
        (307, "GET", "/api/analytics/user-retention"),
        (311, "GET", "/api/analytics/revenue"),
        (315, "POST", "/api/analytics/custom-report"),
        (321, "POST", "/api/assets/upload"),
        (327, "DELETE", "/api/cache/clear"),
        (331, "POST", "/api/cache/warmup"),
        (335, "PUT", "/api/cache/set"),
        (339, "DELETE", "/api/cache/delete"),
        (345, "GET", "/api/characters/categories"),
        (349, "GET", "/api/characters/list"),
        (353, "GET", "/api/characters/{id}"),
        (357, "GET", "/api/characters/recommendations"),
        (361, "POST", "/api/characters/bind"),
        (365, "GET", "/api/characters/bindings"),
        (369, "PUT", "/api/characters/bindings/{id}"),
        (373, "DELETE", "/api/characters/unbind"),
        (379, "GET", "/api/config/system"),
        (383, "PUT", "/api/config/system"),
        (387, "GET", "/api/config/user"),
        (391, "PUT", "/api/config/user"),
        (395, "GET", "/api/config/ai"),
        (399, "PUT", "/api/config/ai"),
        (403, "POST", "/api/config/reset"),
        (409, "POST", "/api/credits/check"),
        (413, "POST", "/api/credits/freeze"),
        (417, "POST", "/api/credits/refund"),
        (423, "GET", "/api/points/balance"),
        (427, "POST", "/api/points/recharge"),
        (431, "GET", "/api/points/transactions"),
        (437, "GET", "/api/notifications"),
        (441, "PUT", "/api/notifications/mark-read"),
        (445, "PUT", "/api/notifications/mark-all-read"),
        (449, "DELETE", "/api/notifications/{id}"),
        (453, "GET", "/api/notifications/stats"),
        (457, "POST", "/api/notifications/send"),
        (463, "GET", "/api/permissions/user"),
        (467, "POST", "/api/permissions/check"),
        (471, "GET", "/api/permissions/roles"),
        (475, "PUT", "/api/permissions/assign-role"),
        (479, "POST", "/api/permissions/grant"),
        (483, "DELETE", "/api/permissions/revoke"),
        (487, "GET", "/api/permissions/history"),
        (493, "GET", "/api/user/profile"),
        (497, "PUT", "/api/user/profile"),
        (501, "PUT", "/api/user/preferences"),
        (505, "GET", "/api/user/preferences"),
        (511, "POST", "/api/templates/create"),
        (515, "POST", "/api/templates/{id}/use"),
        (519, "GET", "/api/templates/marketplace"),
        (523, "GET", "/api/templates/my-templates"),
        (527, "GET", "/api/templates/{id}/detail"),
        (531, "PUT", "/api/templates/{id}"),
        (535, "DELETE", "/api/templates/{id}"),
        (545, "POST", "/api/characters/generate"),
        (551, "POST", "/api/stories/generate"),
        (555, "GET", "/api/stories/{id}/status"),
        (561, "POST", "/api/ai/generate"),
        (565, "GET", "/api/ai/tasks/{id}"),
        (569, "GET", "/api/ai/tasks"),
        (573, "POST", "/api/ai/tasks/{id}/retry"),
        (579, "POST", "/api/audio/mix"),
        (583, "GET", "/api/audio/mix/{id}/status"),
        (588, "GET", "/api/audio/enhance/{id}/status"),
        (594, "POST", "/api/batch/images/generate"),
        (598, "POST", "/api/batch/voices/synthesize"),
        (602, "POST", "/api/batch/music/generate"),
        (606, "GET", "/api/batch/{batch_id}/status"),
        (610, "DELETE", "/api/batch/{batch_id}"),
        (616, "POST", "/api/exports/create"),
        (620, "GET", "/api/exports/list"),
        (624, "GET", "/api/exports/{id}/status"),
        (628, "GET", "/api/exports/{id}/download"),
        (632, "POST", "/api/general-exports/create"),
        (636, "GET", "/api/general-exports/{id}/status"),
        (640, "GET", "/api/general-exports/{id}/download"),
        (644, "GET", "/api/general-exports/list"),
        (648, "POST", "/api/general-exports/{id}/cancel"),
        (652, "DELETE", "/api/general-exports/{id}"),
        (656, "POST", "/api/general-exports/batch"),
        (662, "GET", "/api/downloads/list"),
        (666, "POST", "/api/downloads/{id}/retry"),
        (670, "GET", "/api/downloads/statistics"),
        (674, "POST", "/api/downloads/create-link"),
        (678, "GET", "/api/downloads/secure/{token}"),
        (682, "POST", "/api/downloads/batch"),
        (686, "POST", "/api/downloads/cleanup"),
        (694, "POST", "/api/files/upload"),
        (698, "GET", "/api/files/list"),
        (702, "GET", "/api/files/{id}"),
        (706, "DELETE", "/api/files/{id}"),
        (710, "GET", "/api/files/{id}/download"),
        (716, "POST", "/api/images/generate"),
        (720, "GET", "/api/images/{task_id}/status"),
        (724, "POST", "/api/images/{task_id}/switch-platform"),
        (728, "POST", "/api/images/batch-generate"),
        (732, "GET", "/api/images/{id}/result"),
        (736, "GET", "/api/images/history"),
        (742, "GET", "/api/logs/system"),
        (746, "GET", "/api/logs/user-actions"),
        (750, "GET", "/api/logs/ai-calls"),
        (754, "GET", "/api/logs/errors"),
        (758, "PUT", "/api/logs/errors/{id}/resolve"),
        (762, "POST", "/api/logs/export"),
        (768, "POST", "/api/music/generate"),
        (772, "GET", "/api/music/{task_id}/status"),
        (776, "GET", "/api/music/styles"),
        (780, "GET", "/api/music/{id}/result"),
        (784, "POST", "/api/batch/music/generate"),
        (790, "POST", "/api/projects/create-with-story"),
        (794, "PUT", "/api/projects/{id}/confirm-title"),
        (798, "GET", "/api/projects/my-projects"),
        (802, "GET", "/api/projects/{id}"),
        (806, "GET", "/api/projects/list"),
        (810, "POST", "/api/projects/create"),
        (814, "PUT", "/api/projects/{id}"),
        (818, "DELETE", "/api/projects/{id}"),
        (824, "POST", "/api/project-management/tasks"),
        (828, "GET", "/api/project-management/progress"),
        (832, "POST", "/api/project-management/assign-resources"),
        (836, "GET", "/api/project-management/statistics"),
        (840, "POST", "/api/project-management/collaborate"),
        (844, "GET", "/api/project-management/milestones"),
        (850, "POST", "/api/publications/publish"),
        (854, "GET", "/api/publications/{id}/status"),
        (858, "PUT", "/api/publications/{id}"),
        (862, "DELETE", "/api/publications/{id}"),
        (866, "POST", "/api/publications/{id}/unpublish"),
        (870, "GET", "/api/publications/my-publications"),
        (874, "GET", "/api/publications/plaza"),
        (878, "GET", "/api/publications/{id}/detail"),
        (882, "GET", "/api/publications/trending"),
        (888, "GET", "/api/recommendations/content"),
        (892, "GET", "/api/recommendations/users"),
        (896, "GET", "/api/recommendations/topics"),
        (900, "POST", "/api/recommendations/feedback"),
        (904, "GET", "/api/recommendations/preferences"),
        (908, "PUT", "/api/recommendations/preferences"),
        (912, "GET", "/api/recommendations/analytics"),
        (916, "GET", "/api/recommendations/personalized"),
        (922, "POST", "/api/resources/generate"),
        (926, "GET", "/api/resources/{id}/status"),
        (930, "GET", "/api/resources/list"),
        (934, "DELETE", "/api/resources/{id}"),
        (938, "GET", "/api/resources/{id}/download-info"),
        (942, "POST", "/api/resources/{id}/confirm-download"),
        (946, "GET", "/api/resources/my-resources"),
        (950, "PUT", "/api/resources/{id}/status"),
        (954, "POST", "/api/batch/resources/generate"),
        (960, "POST", "/api/reviews/submit"),
        (964, "GET", "/api/reviews/{id}/status"),
        (968, "POST", "/api/reviews/{id}/appeal"),
        (972, "GET", "/api/reviews/my-reviews"),
        (976, "GET", "/api/reviews/queue-status"),
        (980, "GET", "/api/reviews/guidelines"),
        (984, "POST", "/api/reviews/pre-check"),
        (990, "POST", "/api/social/follow"),
        (994, "GET", "/api/social/follows"),
        (998, "POST", "/api/social/like"),
        (1002, "POST", "/api/social/comment"),
        (1006, "GET", "/api/social/comments"),
        (1010, "POST", "/api/social/share"),
        (1014, "GET", "/api/social/feed"),
        (1018, "GET", "/api/social/notifications"),
        (1022, "POST", "/api/social/mark-notifications-read"),
        (1026, "GET", "/api/social/stats"),
        (1032, "POST", "/api/sounds/generate"),
        (1036, "GET", "/api/sounds/{id}/status"),
        (1040, "GET", "/api/sounds/{id}/result"),
        (1044, "POST", "/api/batch/sounds/generate"),
        (1050, "POST", "/api/videos/generate"),
        (1054, "GET", "/api/videos/{task_id}/status"),
        (1058, "GET", "/api/videos/platform-comparison"),
        (1062, "GET", "/api/videos/{id}/result"),
        (1068, "POST", "/api/voices/synthesize"),
        (1072, "GET", "/api/voices/{task_id}/status"),
        (1076, "GET", "/api/voices/platform-comparison"),
        (1080, "POST", "/api/voices/batch-synthesize"),
        (1084, "POST", "/api/voices/clone"),
        (1088, "GET", "/api/voices/clone/{id}/status"),
        (1092, "POST", "/api/voices/custom"),
        (1096, "GET", "/api/voices/custom/{id}/status"),
        (1100, "POST", "/api/voices/{id}/preview"),
        (1104, "GET", "/api/voices/history"),
        (1110, "POST", "/api/works/publish"),
        (1114, "PUT", "/api/works/{id}"),
        (1118, "DELETE", "/api/works/{id}"),
        (1122, "GET", "/api/works/my-works"),
        (1126, "GET", "/api/works/gallery"),
        (1130, "GET", "/api/works/{id}/share"),
        (1134, "POST", "/api/works/{id}/like"),
        (1138, "GET", "/api/works/trending"),
        (1144, "POST", "/api/workflows"),
        (1148, "GET", "/api/workflows"),
        (1152, "GET", "/api/workflows/{id}"),
        (1156, "POST", "/api/workflows/{id}/execute"),
        (1160, "GET", "/api/workflows/executions/{execution_id}"),
        (1164, "POST", "/api/workflows/executions/{execution_id}/input"),
        (1168, "DELETE", "/api/workflows/executions/{execution_id}"),
        (1172, "GET", "/api/workflows/{id}/executions"),
        (1178, "GET", "/api/user-growth/profile"),
        (1182, "GET", "/api/user-growth/leaderboard"),
        (1186, "POST", "/api/user-growth/complete-achievement"),
        (1190, "GET", "/api/user-growth/daily-tasks"),
        (1194, "POST", "/api/user-growth/complete-daily-task"),
        (1198, "GET", "/api/user-growth/history"),
        (1202, "GET", "/api/user-growth/statistics"),
        (1206, "POST", "/api/user-growth/set-goals"),
        (1210, "GET", "/api/user-growth/recommendations"),
        (1214, "GET", "/api/user-growth/milestones"),
    ]
    
    return doc_content

def extract_route_apis() -> List[str]:
    """从路由文件中提取API接口"""
    route_apis = []
    
    # 从搜索结果中提取的路由API接口（需要加上/api前缀）
    route_content = [
        ("post", "/login"),
        ("post", "/register"),
        ("get", "/check"),
        ("get", "/captcha/generate"),
        ("post", "/captcha/verify"),
        ("post", "/captcha/refresh"),
        ("get", "/system/health"),
        ("get", "/system/metrics"),
        ("get", "/system/response-time"),
        ("get", "/system/monitor/overview"),
        ("get", "/system/monitor/metrics"),
        ("get", "/system/search"),
        ("get", "/app-monitor/health"),
        ("get", "/app-monitor/metrics"),  # 注意：这个在文档中是 /api/monitor/metrics
        ("get", "/app-monitor/realtime"),  # 注意：这个在文档中是 /api/monitor/realtime
        ("get", "/app-monitor/alerts"),   # 注意：这个在文档中是 /api/monitor/alerts
        ("get", "/cache/stats"),
        ("get", "/cache/keys"),
        ("get", "/cache/get"),
        ("get", "/cache/config"),
        ("get", "/styles/list"),
        ("get", "/styles/popular"),
        ("get", "/styles/{id}"),
        ("get", "/user/profile"),
        ("put", "/user/profile"),
        ("get", "/user/preferences"),
        ("put", "/user/preferences"),
        ("get", "/points/balance"),
        ("post", "/points/recharge"),
        ("get", "/points/transactions"),
        ("post", "/credits/check"),
        ("post", "/credits/freeze"),
        ("post", "/credits/refund"),
        ("get", "/ai-models/available"),
        ("get", "/ai-models/favorites"),
        ("get", "/ai-models/list"),
        ("post", "/ai-models/switch"),
        ("get", "/ai-models/usage-stats"),
        ("get", "/ai-models/{model_id}/detail"),
        ("post", "/ai-models/{model_id}/test"),
        ("post", "/ai-models/{model_id}/favorite"),
        ("post", "/ai-models/select-platform"),  # 这个在文档中不存在
        ("get", "/ai-models/platforms-health"),  # 这个在文档中不存在
        ("get", "/ai-models/platform-health/{platform}"),  # 这个在文档中不存在
        ("get", "/ai-models/platform-stats/{platform}"),   # 这个在文档中不存在
        ("get", "/assets/list"),
        ("post", "/assets/upload"),
        ("get", "/assets/{id}"),
        ("delete", "/assets/{id}"),
        ("delete", "/cache/clear"),
        ("post", "/cache/warmup"),
        ("put", "/cache/set"),
        ("delete", "/cache/delete"),
        ("get", "/characters/categories"),
        ("get", "/characters/list"),
        ("get", "/characters/recommendations"),
        ("get", "/characters/bindings"),
        ("post", "/characters/bind"),
        ("delete", "/characters/unbind"),
        ("post", "/characters/generate"),
        ("put", "/characters/bindings/{id}"),
        ("get", "/characters/{id}"),
        ("get", "/config/system"),
        ("put", "/config/system"),
        ("get", "/config/user"),
        ("put", "/config/user"),
        ("get", "/config/ai"),
        ("put", "/config/ai"),
        ("post", "/config/reset"),
        ("get", "/notifications"),
        ("put", "/notifications/mark-read"),
        ("put", "/notifications/mark-all-read"),
        ("get", "/notifications/stats"),
        ("post", "/notifications/send"),
        ("delete", "/notifications/{id}"),
        ("post", "/templates/create"),
        ("get", "/templates/marketplace"),
        ("get", "/templates/my-templates"),
        ("post", "/templates/{id}/use"),
        ("get", "/templates/{id}/detail"),
        ("put", "/templates/{id}"),
        ("delete", "/templates/{id}"),
        ("get", "/tasks"),
        ("get", "/tasks/{id}"),
        ("get", "/tasks/stats"),
        ("get", "/tasks/timeout-config"),
        ("post", "/tasks/{id}/cancel"),
        ("post", "/tasks/{id}/retry"),
        ("get", "/tasks/batch/status"),
        ("get", "/tasks/{id}/recovery"),
        ("get", "/versions/compare"),
        ("post", "/resources/{id}/versions"),
        ("get", "/resources/{id}/versions"),
        ("get", "/versions/{id}"),
        ("put", "/versions/{id}/set-current"),
        ("delete", "/versions/{id}"),
        ("post", "/exports/create"),
        ("get", "/exports/list"),
        ("get", "/exports/{id}/status"),
        ("get", "/exports/{id}/download"),
        ("post", "/general-exports/create"),
        ("get", "/general-exports/list"),
        ("post", "/general-exports/batch"),
        ("get", "/general-exports/{id}/status"),
        ("get", "/general-exports/{id}/download"),
        ("post", "/general-exports/{id}/cancel"),
        ("delete", "/general-exports/{id}"),
        ("get", "/downloads/list"),
        ("get", "/downloads/statistics"),
        ("post", "/downloads/create-link"),
        ("post", "/downloads/batch"),
        ("post", "/downloads/cleanup"),
        ("get", "/downloads/secure/{token}"),
        ("post", "/downloads/{id}/retry"),
        ("post", "/files/upload"),
        ("get", "/files/list"),
        ("get", "/files/{id}"),
        ("delete", "/files/{id}"),
        ("get", "/files/{id}/download"),
        ("get", "/logs/system"),
        ("get", "/logs/user-actions"),
        ("get", "/logs/ai-calls"),
        ("get", "/logs/errors"),
        ("post", "/logs/export"),
        ("put", "/logs/errors/{id}/resolve"),
        ("post", "/projects/create-with-story"),
        ("get", "/projects/my-projects"),
        ("get", "/projects/list"),
        ("post", "/projects/create"),
        ("put", "/projects/{id}/confirm-title"),
        ("get", "/projects/{id}"),
        ("put", "/projects/{id}"),
        ("delete", "/projects/{id}"),
        ("post", "/project-management/tasks"),
        ("get", "/project-management/progress"),
        ("post", "/project-management/assign-resources"),
        ("get", "/project-management/statistics"),
        ("post", "/project-management/collaborate"),
        ("get", "/project-management/milestones"),
        ("post", "/ai/text/generate"),  # 注意：这个在文档中是 /api/ai/generate
        ("get", "/ai/tasks"),
        ("get", "/ai/tasks/{id}"),
        ("post", "/ai/tasks/{id}/retry"),
        ("post", "/images/generate"),
        ("post", "/images/batch-generate"),
        ("get", "/images/{id}/status"),  # 注意：文档中是 {task_id}
        ("get", "/images/{id}/result"),
        ("post", "/audio/mix"),
        ("post", "/audio/enhance"),  # 这个在文档中不存在
        ("get", "/audio/mix/{id}/status"),
        ("get", "/audio/enhance/{id}/status"),
        ("post", "/music/generate"),
        ("post", "/music/batch-generate"),  # 注意：这个在文档中是 /api/batch/music/generate
        ("get", "/music/{id}/status"),  # 注意：文档中是 {task_id}
        ("get", "/music/{id}/result"),
        ("post", "/sounds/generate"),
        ("post", "/sounds/batch-generate"),  # 注意：这个在文档中是 /api/batch/sounds/generate
        ("get", "/sounds/{id}/status"),
        ("get", "/sounds/{id}/result"),
        ("post", "/videos/generate"),
        ("get", "/videos/{id}/status"),  # 注意：文档中是 {task_id}
        ("get", "/videos/{id}/result"),
        ("post", "/voice/synthesize"),  # 注意：文档中是 /api/voices/synthesize
        ("post", "/voice/batch-synthesize"),  # 注意：文档中是 /api/voices/batch-synthesize
        ("post", "/voice/clone"),  # 注意：文档中是 /api/voices/clone
        ("post", "/voice/custom"),  # 注意：文档中是 /api/voices/custom
        ("post", "/voice/preview"),  # 注意：文档中是 /api/voices/{id}/preview
        ("get", "/voice/custom/{id}/status"),  # 注意：文档中是 /api/voices/custom/{id}/status
        ("get", "/voice/clone/{id}/status"),  # 注意：文档中是 /api/voices/clone/{id}/status
        ("get", "/voice/{id}/status"),  # 注意：文档中是 /api/voices/{task_id}/status
        ("post", "/stories/generate"),
        ("get", "/stories/{id}/status"),
        ("post", "/resources/generate"),
        ("get", "/resources/list"),
        ("get", "/resources/my-resources"),
        ("post", "/resources/batch-generate"),  # 注意：这个在文档中是 /api/batch/resources/generate
        ("put", "/resources/{id}/status"),
        ("get", "/resources/{id}/status"),
        ("get", "/resources/{id}/download-info"),
        ("post", "/resources/{id}/confirm-download"),
        ("delete", "/resources/{id}"),
        ("post", "/publications/publish"),
        ("get", "/publications/my-publications"),
        ("get", "/publications/plaza"),
        ("get", "/publications/trending"),
        ("get", "/publications/{id}/status"),
        ("post", "/publications/{id}/unpublish"),
        ("get", "/publications/{id}/detail"),
        ("put", "/publications/{id}"),
        ("delete", "/publications/{id}"),
        ("get", "/recommendations/content"),
        ("get", "/recommendations/users"),
        ("get", "/recommendations/topics"),
        ("post", "/recommendations/feedback"),
        ("get", "/recommendations/preferences"),
        ("put", "/recommendations/preferences"),
        ("get", "/recommendations/analytics"),
        ("get", "/recommendations/personalized"),
        ("post", "/reviews/submit"),
        ("get", "/reviews/my-reviews"),
        ("get", "/reviews/queue-status"),
        ("get", "/reviews/guidelines"),
        ("post", "/reviews/pre-check"),
        ("get", "/reviews/{id}/status"),
        ("post", "/reviews/{id}/appeal"),
        ("post", "/social/follow"),
        ("get", "/social/follows"),
        ("post", "/social/like"),
        ("post", "/social/comment"),
        ("post", "/social/share"),
        ("get", "/social/feed"),
        ("get", "/social/notifications"),
        ("put", "/social/notifications/read"),  # 注意：文档中是 POST /api/social/mark-notifications-read
        ("get", "/social/stats"),
        ("get", "/social/{id}/comments"),  # 注意：文档中是 GET /api/social/comments
        ("post", "/works/publish"),
        ("get", "/works/my-works"),
        ("get", "/works/gallery"),
        ("get", "/works/trending"),
        ("get", "/works/{id}/share-link"),  # 注意：文档中是 GET /api/works/{id}/share
        ("post", "/works/{id}/like"),
        ("put", "/works/{id}"),
        ("delete", "/works/{id}"),
        ("post", "/workflows/create"),  # 注意：文档中是 POST /api/workflows
        ("get", "/workflows"),
        ("post", "/workflows/{id}/execute"),
        ("get", "/workflows/{id}/execution-status"),  # 注意：文档中是 GET /api/workflows/executions/{execution_id}
        ("post", "/workflows/{id}/step-input"),  # 注意：文档中是 POST /api/workflows/executions/{execution_id}/input
        ("post", "/workflows/{id}/cancel"),  # 注意：文档中是 DELETE /api/workflows/executions/{execution_id}
        ("get", "/workflows/{id}/history"),  # 注意：文档中是 GET /api/workflows/{id}/executions
        ("get", "/workflows/{id}"),
        ("get", "/user-growth/profile"),
        ("get", "/user-growth/leaderboard"),
        ("get", "/user-growth/daily-tasks"),
        ("get", "/user-growth/history"),
        ("get", "/user-growth/statistics"),
        ("post", "/user-growth/goals"),  # 注意：文档中是 POST /api/user-growth/set-goals
        ("get", "/user-growth/recommendations"),
        ("get", "/user-growth/milestones"),
        ("post", "/user-growth/daily-tasks/{id}/complete"),  # 注意：文档中是 POST /api/user-growth/complete-daily-task
        ("post", "/user-growth/achievements/{id}/complete"),  # 注意：文档中是 POST /api/user-growth/complete-achievement
        ("put", "/app-monitor/alerts/{id}/acknowledge"),  # 注意：文档中是 PUT /api/monitor/alerts/{id}/acknowledge
        ("put", "/app-monitor/alerts/{id}/resolve"),  # 注意：文档中是 PUT /api/monitor/alerts/{id}/resolve
        ("post", "/styles/create"),
        ("post", "/permissions/check"),
        ("get", "/permissions/roles"),
        ("put", "/permissions/assign-role"),
        ("post", "/permissions/grant"),
        ("delete", "/permissions/revoke"),
        ("get", "/permissions/history"),
        ("get", "/permissions/user/{id}"),  # 注意：文档中是 GET /api/permissions/user
        ("get", "/analytics/user-behavior"),
        ("get", "/analytics/system-usage"),
        ("get", "/analytics/ai-performance"),
        ("get", "/analytics/user-retention"),
        ("get", "/analytics/revenue"),
        ("post", "/analytics/custom-report"),
        ("get", "/ads/list"),  # 这个在文档中不存在
        ("post", "/ads/create"),  # 这个在文档中不存在
        ("post", "/websocket/auth"),
        ("get", "/websocket/sessions"),
        ("delete", "/websocket/disconnect"),  # 注意：文档中是 POST
        ("get", "/websocket/status"),
    ]
    
    # 转换为带/api前缀的格式
    for method, path in route_content:
        route_apis.append(f"{method.upper()} /api{path}")
    
    return route_apis

def main():
    """主函数：执行差异化分析"""
    print("=== API接口差异化分析报告 ===\n")
    
    # 提取数据
    doc_apis = extract_doc_apis()
    route_apis = extract_route_apis()
    
    # 创建集合用于对比
    doc_set = set()
    for line_num, method, path in doc_apis:
        doc_set.add(f"{method} {path}")
    
    route_set = set(route_apis)
    
    # 统计数量
    print(f"检测数量：文档api接口({len(doc_set)})个，实际存在控制器的api接口({len(route_set)})个\n")
    
    # 找出差异
    doc_only = doc_set - route_set
    route_only = route_set - doc_set
    
    print("=== apitest-code.mdc中独立存在的api接口 ===")
    doc_only_with_lines = []
    for line_num, method, path in doc_apis:
        api_str = f"{method} {path}"
        if api_str in doc_only:
            doc_only_with_lines.append((line_num, api_str))
    
    # 按行号排序
    doc_only_with_lines.sort(key=lambda x: x[0])
    
    for line_num, api_str in doc_only_with_lines:
        print(f"{line_num} {api_str}")
    
    print(f"\n文档中独立存在的接口总数：{len(doc_only)}个\n")
    
    print("=== 控制器中独立存在的api接口 ===")
    for api_str in sorted(route_only):
        # 从API路径推断控制器名
        method, path = api_str.split(' ', 1)
        path_parts = path.split('/')
        if len(path_parts) >= 3:
            controller_name = path_parts[2].title() + "Controller"
            print(f"{controller_name} {api_str}")
        else:
            print(f"UnknownController {api_str}")
    
    print(f"\n控制器中独立存在的接口总数：{len(route_only)}个")

if __name__ == "__main__":
    main()
