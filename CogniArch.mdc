# CogniArch 战略蓝图

## 任务概述
执行 API 接口差异化检测任务，制定争议解决方案，优先处理功能相同但URL不同的接口统一问题。

## 执行状态
- [x] 获取 apitest-code.mdc 文档内容
- [x] 获取控制器目录结构
- [x] 获取路由定义文件内容
- [x] 系统性分析API接口差异
- [x] 生成差异化报告
- [x] 接收 @CogniAud 审计报告
- [x] 制定争议解决方案

## 🎯 **争议解决方案**

基于 @CogniAud 的审计发现，遵循 [原则 0: 权威层级原则]，制定以下解决方案：

### 📋 **权威依据**
1. **用户指令**：确保API设计的一致性和规范性
2. **最高标准文档**：`@.cursor/rules/index.mdc` 和 `@.cursor/rules/dev-api-guidelines-add.mdc`
3. **技术实现**：现有控制器架构
4. **系统稳定性**：最小化破坏性变更

### 🚨 **第一优先级：路径统一方案**

#### 1. 监控告警接口统一
**决策**：采用文档规划的路径 `/api/monitor/*`
**理由**：符合业务语义，更简洁明确
**执行方案**：
- 修改路由：`app-monitor` → `monitor`
- 保持控制器名称：`AppMonitorController`
- 更新文档：无需修改

#### 2. 语音合成接口统一
**决策**：采用文档规划的路径 `/api/voices/*`
**理由**：复数形式符合RESTful规范
**执行方案**：
- 修改路由：`voice` → `voices`
- 保持控制器名称：`VoiceController`
- 更新文档：无需修改

#### 3. 批量操作接口统一
**决策**：采用文档规划的统一批量接口设计
**理由**：提高API架构一致性
**执行方案**：
- 创建统一的 `BatchController`
- 迁移分散的批量操作到统一接口
- 保持向后兼容性

### 🔧 **第二优先级：缺失功能实现**

#### 1. AuthController缺失方法实现
**必须实现的方法**：
- `logout()` - 用户登出
- `refresh()` - 刷新Token
- `forgotPassword()` - 忘记密码
- `resetPassword()` - 重置密码
- `verify()` - 验证Token

#### 2. AdController完整实现
**必须实现的方法**：
- `store()` - 广告开始
- `update()` - 广告结束

### 🏗️ **第三优先级：架构优化**

#### 1. 工作流执行接口设计统一
**决策**：采用更清晰的URL设计模式
**方案**：结合两种设计的优点
- 保持简洁的路径结构
- 增强语义表达能力

#### 2. API版本控制准备
**建议**：为未来API版本升级做准备
**方案**：在路由设计中预留版本控制空间

### 📊 **实施计划**

#### 阶段1：路径统一（1-2天）
1. 修改路由文件中的路径定义
2. 更新相关控制器的路由注释
3. 执行回归测试

#### 阶段2：功能补全（3-5天）
1. 实现AuthController缺失方法
2. 创建AdController及其方法
3. 实现批量操作统一接口

#### 阶段3：架构优化（2-3天）
1. 优化工作流接口设计
2. 完善API文档同步
3. 执行全面测试

### ⚠️ **风险控制**

#### 1. 向后兼容性
- 保持旧路径的临时重定向
- 逐步废弃旧接口
- 提供迁移指南

#### 2. 测试覆盖
- 所有修改的接口必须通过测试
- 新增接口必须编写单元测试
- 执行集成测试验证

#### 3. 文档同步
- 同步更新所有相关文档
- 确保九重同步原则的执行
- 维护API变更日志

## 应用规则
- 严格遵循 @.cursor/rules/ 文件夹内的所有适用规则
- 遵循API接口增/删/改铁律的九重同步原则
- 使用 Claude Sonnet 4 模型进行架构设计

## 应用知识报告
本次争议解决方案应用了以下规则和知识：
- 遵循了 `@.cursor/rules/index.mdc` 中的项目架构规范
- 应用了 `@.cursor/rules/dev-api-guidelines-add.mdc` 中的API设计原则
- 使用了权威层级原则进行决策制定

## 应用模型报告
- 使用模型：Claude Sonnet 4
- 版本：最新版本
- 应用场景：架构设计、争议解决、技术决策制定

@CogniAud 请对此争议解决方案进行规划审计，确认方案的合规性和可行性。
