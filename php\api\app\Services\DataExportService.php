<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\DataExport;
use App\Models\User;
use App\Models\Project;
use App\Models\AiGenerationTask;
use App\Models\CharacterLibrary;
use App\Models\PointsTransaction;
use App\Models\UserFile;
use App\Models\Resource;
use App\Models\ResourceExport;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Response;
use ZipArchive;

/**
 * 数据导出服务
 */
class DataExportService
{
    /**
     * 创建导出任务
     */
    public function createExport(int $userId, string $exportType, string $exportFormat, array $exportParams = [], array $exportFilters = []): array
    {
        try {
            DB::beginTransaction();

            // 检查用户是否有未完成的导出任务
            $pendingExports = DataExport::byUser($userId)
                ->whereIn('status', [DataExport::STATUS_PENDING, DataExport::STATUS_PROCESSING])
                ->count();

            if ($pendingExports >= 3) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '您有太多未完成的导出任务，请稍后再试',
                    'data' => []
                ];
            }

            // 创建导出任务
            $export = DataExport::create([
                'user_id' => $userId,
                'export_type' => $exportType,
                'export_format' => $exportFormat,
                'export_params' => $exportParams,
                'export_filters' => $exportFilters,
                'status' => DataExport::STATUS_PENDING
            ]);

            DB::commit();

            // 异步执行导出任务
            $this->executeExport($export);

            Log::info('导出任务创建成功', [
                'export_id' => $export->id,
                'user_id' => $userId,
                'export_type' => $exportType,
                'export_format' => $exportFormat
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '导出任务创建成功',
                'data' => [
                    'export_id' => $export->id,
                    'export_type' => $export->export_type,
                    'export_format' => $export->export_format,
                    'status' => $export->status,
                    'created_at' => $export->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('创建导出任务失败', [
                'user_id' => $userId,
                'export_type' => $exportType,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '创建导出任务失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取用户导出任务列表
     */
    public function getUserExports(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = DataExport::byUser($userId);

            // 应用筛选条件
            if (!empty($filters['export_type'])) {
                $query->byType($filters['export_type']);
            }

            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }

            $exports = $query->orderByCreated()->paginate($perPage, ['*'], 'page', $page);

            $exportsData = $exports->map(function ($export) {
                return [
                    'id' => $export->id,
                    'export_type' => $export->export_type,
                    'export_format' => $export->export_format,
                    'status' => $export->status,
                    'file_size' => $export->file_size,
                    'human_file_size' => $export->human_file_size,
                    'record_count' => $export->record_count,
                    'progress_percentage' => $export->getProgressPercentage(),
                    'download_count' => $export->download_count,
                    'is_downloadable' => $export->isDownloadable(),
                    'error_message' => $export->error_message,
                    'created_at' => $export->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $export->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $export->completed_at?->format('Y-m-d H:i:s'),
                    'expires_at' => $export->expires_at?->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'exports' => $exportsData,
                    'pagination' => [
                        'current_page' => $exports->currentPage(),
                        'total' => $exports->total(),
                        'per_page' => $exports->perPage(),
                        'last_page' => $exports->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取导出任务列表失败', [
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取导出任务列表失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取导出任务状态
     */
    public function getExportStatus(int $exportId, int $userId): array
    {
        try {
            $export = DataExport::byUser($userId)->find($exportId);

            if (!$export) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'id' => $export->id,
                    'export_type' => $export->export_type,
                    'export_format' => $export->export_format,
                    'status' => $export->status,
                    'progress' => $export->progress_info ?? [],
                    'error_message' => $export->error_message,
                    'created_at' => $export->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $export->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $export->completed_at?->format('Y-m-d H:i:s'),
                    'expires_at' => $export->expires_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取导出任务状态失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取导出任务状态失败',
                'data' => []
            ];
        }
    }

    /**
     * 下载导出文件
     */
    public function downloadExport(int $exportId, int $userId): array
    {
        try {
            $export = DataExport::byUser($userId)->find($exportId);

            if (!$export) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在',
                    'data' => []
                ];
            }

            if (!$export->isDownloadable()) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '文件不可下载',
                    'data' => []
                ];
            }

            // 增加下载次数
            $export->incrementDownload();

            $filename = $this->getExportFilename($export);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'download_url' => Storage::url($export->file_path),
                    'filename' => $filename,
                    'file_size' => $export->file_size,
                    'human_file_size' => $export->human_file_size,
                    'expires_at' => $export->expires_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('下载导出文件失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '下载导出文件失败',
                'data' => []
            ];
        }
    }

    /**
     * 执行导出任务（模拟实现）
     */
    private function executeExport(DataExport $export): void
    {
        try {
            $export->start();

            // 根据导出类型获取数据
            $data = $this->getExportData($export);
            
            // 模拟导出进度
            $export->updateProgress(0, count($data), '开始导出...');
            
            // 模拟处理延迟
            sleep(1);
            
            $export->updateProgress(count($data) / 2, count($data), '导出进行中...');
            sleep(1);
            
            // 生成导出文件
            $filePath = $this->generateExportFile($export, $data);
            $fileSize = Storage::size($filePath);
            
            $export->complete($filePath, $fileSize, count($data));

            Log::info('导出任务完成', [
                'export_id' => $export->id,
                'record_count' => count($data),
                'file_size' => $fileSize
            ]);

        } catch (\Exception $e) {
            $export->fail($e->getMessage());
            
            Log::error('导出任务失败', [
                'export_id' => $export->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取导出数据
     */
    private function getExportData(DataExport $export): array
    {
        switch ($export->export_type) {
            case DataExport::TYPE_USER_DATA:
                return $this->getUserData($export->user_id);
            
            case DataExport::TYPE_PROJECTS:
                return $this->getProjectsData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_AI_TASKS:
                return $this->getAiTasksData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_CHARACTERS:
                return $this->getCharactersData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_POINTS_HISTORY:
                return $this->getPointsHistoryData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_FILES:
                return $this->getFilesData($export->user_id, $export->export_filters);
            
            default:
                return [];
        }
    }

    /**
     * 生成导出文件
     */
    private function generateExportFile(DataExport $export, array $data): string
    {
        $filename = 'export_' . $export->id . '_' . date('Ymd_His') . '.' . $export->export_format;
        $filePath = 'exports/' . $filename;

        switch ($export->export_format) {
            case DataExport::FORMAT_CSV:
                $content = $this->generateCsv($data);
                break;
            
            case DataExport::FORMAT_JSON:
                $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                break;
            
            default:
                $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                break;
        }

        Storage::put($filePath, $content);
        
        return $filePath;
    }

    /**
     * 生成CSV内容
     */
    private function generateCsv(array $data): string
    {
        if (empty($data)) {
            return '';
        }

        $csv = '';
        $headers = array_keys($data[0]);
        $csv .= implode(',', $headers) . "\n";

        foreach ($data as $row) {
            $csv .= implode(',', array_map(function($value) {
                return '"' . str_replace('"', '""', $value) . '"';
            }, array_values($row))) . "\n";
        }

        return $csv;
    }

    /**
     * 获取用户数据
     */
    private function getUserData(int $userId): array
    {
        $user = User::find($userId);
        return [
            [
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'points' => $user->points,
                'created_at' => $user->created_at->format('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 获取项目数据
     */
    private function getProjectsData(int $userId, array $filters): array
    {
        $projects = Project::where('user_id', $userId)->get();
        
        return $projects->map(function($project) {
            return [
                'id' => $project->id,
                'title' => $project->title,
                'description' => $project->description,
                'status' => $project->status,
                'created_at' => $project->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取AI任务数据
     */
    private function getAiTasksData(int $userId, array $filters): array
    {
        $tasks = AiGenerationTask::where('user_id', $userId)->get();
        
        return $tasks->map(function($task) {
            return [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'status' => $task->status,
                'cost' => $task->cost,
                'created_at' => $task->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取角色数据
     */
    private function getCharactersData(int $userId, array $filters): array
    {
        // 模拟返回用户绑定的角色数据
        return [
            [
                'id' => 1,
                'name' => '小樱',
                'category' => '动漫角色',
                'binding_date' => Carbon::now()->format('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 获取积分历史数据
     */
    private function getPointsHistoryData(int $userId, array $filters): array
    {
        $transactions = PointsTransaction::where('user_id', $userId)->get();

        return $transactions->map(function($transaction) {
            return [
                'id' => $transaction->id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'description' => $transaction->description,
                'created_at' => $transaction->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取文件数据
     */
    private function getFilesData(int $userId, array $filters): array
    {
        $files = UserFile::where('user_id', $userId)->get();
        
        return $files->map(function($file) {
            return [
                'id' => $file->id,
                'filename' => $file->filename,
                'original_name' => $file->original_name,
                'file_type' => $file->file_type,
                'file_size' => $file->file_size,
                'created_at' => $file->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取导出文件名
     */
    private function getExportFilename(DataExport $export): string
    {
        $typeNames = [
            DataExport::TYPE_USER_DATA => 'user_data',
            DataExport::TYPE_PROJECTS => 'projects',
            DataExport::TYPE_AI_TASKS => 'ai_tasks',
            DataExport::TYPE_CHARACTERS => 'characters',
            DataExport::TYPE_POINTS_HISTORY => 'points_history',
            DataExport::TYPE_FILES => 'files'
        ];

        $typeName = $typeNames[$export->export_type] ?? 'export';
        
        return $typeName . '_export.' . $export->export_format;
    }

    // ==================== 通用导出功能 ====================

    /**
     * 创建通用资源导出任务
     */
    public function createExportTask(int $userId, array $params): array
    {
        try {
            // 验证资源ID
            $resourceIds = $params['resource_ids'] ?? [];
            if (empty($resourceIds)) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '资源ID不能为空',
                    'data' => []
                ];
            }

            // 验证资源是否存在
            $resources = Resource::whereIn('id', $resourceIds)->get();
            if ($resources->count() !== count($resourceIds)) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '部分资源不存在',
                    'data' => []
                ];
            }

            // 计算估算大小和时长
            $estimatedSize = $this->calculateEstimatedSize($resources);
            $estimatedDuration = $this->calculateEstimatedDuration($resources->count());

            DB::beginTransaction();

            // 创建导出记录
            $export = ResourceExport::create([
                'user_id' => $userId,
                'resource_ids' => $resourceIds,
                'export_format' => $params['export_format'] ?? 'zip',
                'export_options' => $params['export_options'] ?? [],
                'include_metadata' => $params['include_metadata'] ?? true,
                'include_versions' => $params['include_versions'] ?? false,
                'compression_level' => $params['compression_level'] ?? 6,
                'status' => ResourceExport::STATUS_PENDING,
                'estimated_size' => $estimatedSize,
                'estimated_duration' => $estimatedDuration,
                'created_at' => Carbon::now()
            ]);

            DB::commit();

            // 异步执行导出任务
            dispatch(function () use ($export) {
                $this->executeGeneralExportTask($export);
            })->afterResponse();

            Log::info('通用导出任务创建成功', [
                'export_id' => $export->id,
                'user_id' => $userId,
                'resource_count' => count($resourceIds)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '导出任务创建成功',
                'data' => [
                    'export_id' => $export->id,
                    'status' => $export->status,
                    'estimated_size' => $this->formatFileSize($estimatedSize),
                    'estimated_duration' => $estimatedDuration . '秒',
                    'created_at' => $export->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建通用导出任务失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '创建导出任务失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取通用导出任务状态
     */
    public function getGeneralExportStatus(int $exportId, int $userId): array
    {
        try {
            $export = ResourceExport::where('id', $exportId)
                ->where('user_id', $userId)
                ->first();

            if (!$export) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'export_id' => $export->id,
                'status' => $export->status,
                'progress' => $export->progress ?? 0,
                'created_at' => $export->created_at->format('Y-m-d H:i:s'),
                'estimated_size' => $this->formatFileSize($export->estimated_size),
                'estimated_duration' => $export->estimated_duration . '秒'
            ];

            if ($export->started_at) {
                $data['started_at'] = $export->started_at->format('Y-m-d H:i:s');
            }

            if ($export->completed_at) {
                $data['completed_at'] = $export->completed_at->format('Y-m-d H:i:s');
                $data['file_size'] = $this->formatFileSize($export->file_size);
                $data['processing_time'] = $export->processing_time_ms . 'ms';
            }

            if ($export->status === ResourceExport::STATUS_COMPLETED && $export->file_path) {
                $data['download_url'] = route('api.exports.download', ['id' => $export->id]);
                $data['expires_at'] = $export->expires_at ? $export->expires_at->format('Y-m-d H:i:s') : null;
            }

            if ($export->error_message) {
                $data['error_message'] = $export->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取导出状态成功',
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('获取通用导出状态失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '获取导出状态失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 下载通用导出文件
     */
    public function downloadGeneralExport(int $exportId, int $userId)
    {
        try {
            $export = ResourceExport::where('id', $exportId)
                ->where('user_id', $userId)
                ->first();

            if (!$export) {
                return response()->json([
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在'
                ], 404);
            }

            if ($export->status !== ResourceExport::STATUS_COMPLETED) {
                return response()->json([
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '导出任务未完成，无法下载'
                ], 400);
            }

            if (!$export->file_path || !Storage::exists($export->file_path)) {
                return response()->json([
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出文件不存在'
                ], 404);
            }

            // 检查文件是否过期
            if ($export->expires_at && Carbon::now()->gt($export->expires_at)) {
                return response()->json([
                    'code' => ApiCodeEnum::EXPIRED,
                    'message' => '导出文件已过期'
                ], 410);
            }

            // 增加下载次数
            $export->increment('download_count');

            Log::info('通用导出文件下载', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'download_count' => $export->download_count + 1
            ]);

            $filePath = Storage::path($export->file_path);
            $filename = 'export_' . $export->id . '_' . date('YmdHis') . '.' . $export->export_format;

            return response()->download($filePath, $filename);

        } catch (\Exception $e) {
            Log::error('下载通用导出文件失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => ApiCodeEnum::ERROR,
                'message' => '下载文件失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取通用导出任务列表
     */
    public function getGeneralExportList(int $userId, array $filters = [], int $page = 1, int $pageSize = 20): array
    {
        try {
            $query = ResourceExport::where('user_id', $userId);

            // 状态过滤
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            // 格式过滤
            if (!empty($filters['export_format'])) {
                $query->where('export_format', $filters['export_format']);
            }

            // 时间范围过滤
            if (!empty($filters['start_date'])) {
                $query->where('created_at', '>=', $filters['start_date']);
            }
            if (!empty($filters['end_date'])) {
                $query->where('created_at', '<=', $filters['end_date']);
            }

            $total = $query->count();
            $exports = $query->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $pageSize)
                ->take($pageSize)
                ->get();

            $data = $exports->map(function ($export) {
                $item = [
                    'export_id' => $export->id,
                    'status' => $export->status,
                    'export_format' => $export->export_format,
                    'resource_count' => count($export->resource_ids),
                    'progress' => $export->progress ?? 0,
                    'created_at' => $export->created_at->format('Y-m-d H:i:s'),
                    'estimated_size' => $this->formatFileSize($export->estimated_size)
                ];

                if ($export->completed_at) {
                    $item['completed_at'] = $export->completed_at->format('Y-m-d H:i:s');
                    $item['file_size'] = $this->formatFileSize($export->file_size);
                }

                if ($export->status === ResourceExport::STATUS_COMPLETED) {
                    $item['download_url'] = route('api.exports.download', ['id' => $export->id]);
                    $item['download_count'] = $export->download_count;
                }

                return $item;
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取导出列表成功',
                'data' => [
                    'exports' => $data,
                    'pagination' => [
                        'current_page' => $page,
                        'page_size' => $pageSize,
                        'total' => $total,
                        'total_pages' => ceil($total / $pageSize)
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取通用导出列表失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '获取导出列表失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 取消通用导出任务
     */
    public function cancelGeneralExport(int $exportId, int $userId): array
    {
        try {
            $export = ResourceExport::where('id', $exportId)
                ->where('user_id', $userId)
                ->first();

            if (!$export) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在',
                    'data' => []
                ];
            }

            if (!$export->isProcessing()) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '只能取消正在进行的导出任务',
                    'data' => []
                ];
            }

            $export->update([
                'status' => ResourceExport::STATUS_CANCELLED,
                'cancelled_at' => Carbon::now()
            ]);

            Log::info('通用导出任务取消成功', [
                'export_id' => $exportId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '导出任务取消成功',
                'data' => [
                    'export_id' => $export->id,
                    'status' => $export->status,
                    'cancelled_at' => $export->cancelled_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('取消通用导出任务失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '取消导出任务失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 删除通用导出任务
     */
    public function deleteGeneralExport(int $exportId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $export = ResourceExport::where('id', $exportId)
                ->where('user_id', $userId)
                ->first();

            if (!$export) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在',
                    'data' => []
                ];
            }

            // 删除相关文件
            $deletedFiles = 0;
            $freedSpace = 0;

            if ($export->file_path && Storage::exists($export->file_path)) {
                $fileSize = Storage::size($export->file_path);
                Storage::delete($export->file_path);
                $deletedFiles++;
                $freedSpace += $fileSize;
            }

            // 删除导出记录
            $export->delete();

            DB::commit();

            Log::info('通用导出任务删除成功', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'deleted_files' => $deletedFiles,
                'freed_space' => $freedSpace
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '导出任务删除成功',
                'data' => [
                    'export_id' => $exportId,
                    'deleted_files' => $deletedFiles,
                    'freed_space' => $this->formatFileSize($freedSpace)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('删除通用导出任务失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '删除导出任务失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 批量创建通用导出任务
     */
    public function batchCreateGeneralExports(int $userId, array $exportConfigs): array
    {
        try {
            DB::beginTransaction();

            $batchId = 'batch_' . time() . '_' . $userId;
            $exportIds = [];
            $totalEstimatedSize = 0;

            foreach ($exportConfigs as $config) {
                $result = $this->createExportTask($userId, $config);
                
                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    $exportIds[] = $result['data']['export_id'];
                    // 解析估算大小（简化处理）
                    $sizeStr = $result['data']['estimated_size'];
                    $totalEstimatedSize += $this->parseSizeString($sizeStr);
                } else {
                    DB::rollBack();
                    return $result;
                }
            }

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量导出任务创建成功',
                'data' => [
                    'batch_id' => $batchId,
                    'export_ids' => $exportIds,
                    'total_count' => count($exportIds),
                    'estimated_total_size' => $this->formatFileSize($totalEstimatedSize)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批量创建通用导出任务失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '批量创建导出任务失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 执行通用导出任务
     */
    private function executeGeneralExportTask(ResourceExport $export): void
    {
        try {
            $export->update([
                'status' => ResourceExport::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 获取资源数据
            $resources = Resource::whereIn('id', $export->resource_ids)->get();
            
            // 根据导出格式执行不同的导出逻辑
            $result = $this->performGeneralExport($export, $resources);

            if ($result['success']) {
                $export->update([
                    'status' => ResourceExport::STATUS_COMPLETED,
                    'file_path' => $result['file_path'],
                    'file_size' => $result['file_size'],
                    'processed_count' => $resources->count(),
                    'progress' => 100,
                    'completed_at' => Carbon::now(),
                    'expires_at' => Carbon::now()->addDays(7), // 7天后过期
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($export->started_at)
                ]);
            } else {
                $export->update([
                    'status' => ResourceExport::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);
            }

        } catch (\Exception $e) {
            $export->update([
                'status' => ResourceExport::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            Log::error('通用导出任务执行失败', [
                'export_id' => $export->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 执行具体的通用导出操作
     */
    private function performGeneralExport(ResourceExport $export, $resources): array
    {
        try {
            $exportDir = 'exports/general/' . date('Y/m/d');
            $filename = 'export_' . $export->id . '_' . time();
            
            switch ($export->export_format) {
                case 'zip':
                    return $this->exportAsZip($export, $resources, $exportDir, $filename);
                case 'json':
                    return $this->exportAsJson($export, $resources, $exportDir, $filename);
                case 'csv':
                    return $this->exportAsCsv($export, $resources, $exportDir, $filename);
                case 'xml':
                    return $this->exportAsXml($export, $resources, $exportDir, $filename);
                case 'pdf':
                    return $this->exportAsPdf($export, $resources, $exportDir, $filename);
                default:
                    return [
                        'success' => false,
                        'error' => '不支持的导出格式：' . $export->export_format
                    ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 导出为ZIP格式
     */
    private function exportAsZip(ResourceExport $export, $resources, string $exportDir, string $filename): array
    {
        $zipPath = $exportDir . '/' . $filename . '.zip';
        $fullPath = Storage::path($zipPath);
        
        // 确保目录存在
        Storage::makeDirectory($exportDir);
        
        $zip = new ZipArchive();
        if ($zip->open($fullPath, ZipArchive::CREATE) !== TRUE) {
            return [
                'success' => false,
                'error' => '无法创建ZIP文件'
            ];
        }
        
        foreach ($resources as $resource) {
            // 添加资源文件到ZIP
            if ($resource->file_path && Storage::exists($resource->file_path)) {
                $zip->addFile(Storage::path($resource->file_path), basename($resource->file_path));
            }
            
            // 如果包含元数据，添加元数据文件
            if ($export->include_metadata) {
                $metadata = [
                    'id' => $resource->id,
                    'name' => $resource->name,
                    'description' => $resource->description,
                    'created_at' => $resource->created_at,
                    'updated_at' => $resource->updated_at
                ];
                $zip->addFromString($resource->id . '_metadata.json', json_encode($metadata, JSON_PRETTY_PRINT));
            }
        }
        
        $zip->close();
        
        return [
            'success' => true,
            'file_path' => $zipPath,
            'file_size' => Storage::size($zipPath)
        ];
    }

    /**
     * 导出为JSON格式
     */
    private function exportAsJson(ResourceExport $export, $resources, string $exportDir, string $filename): array
    {
        $jsonPath = $exportDir . '/' . $filename . '.json';
        
        $data = $resources->map(function ($resource) use ($export) {
            $item = [
                'id' => $resource->id,
                'name' => $resource->name,
                'description' => $resource->description
            ];
            
            if ($export->include_metadata) {
                $item['metadata'] = [
                    'created_at' => $resource->created_at,
                    'updated_at' => $resource->updated_at,
                    'file_size' => $resource->file_size,
                    'file_type' => $resource->file_type
                ];
            }
            
            return $item;
        });
        
        Storage::put($jsonPath, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        return [
            'success' => true,
            'file_path' => $jsonPath,
            'file_size' => Storage::size($jsonPath)
        ];
    }

    /**
     * 导出为CSV格式
     */
    private function exportAsCsv(ResourceExport $export, $resources, string $exportDir, string $filename): array
    {
        $csvPath = $exportDir . '/' . $filename . '.csv';
        $fullPath = Storage::path($csvPath);
        
        // 确保目录存在
        Storage::makeDirectory($exportDir);
        
        $file = fopen($fullPath, 'w');
        
        // 写入BOM以支持中文
        fwrite($file, "\xEF\xBB\xBF");
        
        // 写入标题行
        $headers = ['ID', '名称', '描述'];
        if ($export->include_metadata) {
            $headers = array_merge($headers, ['创建时间', '更新时间', '文件大小', '文件类型']);
        }
        fputcsv($file, $headers);
        
        // 写入数据行
        foreach ($resources as $resource) {
            $row = [
                $resource->id,
                $resource->name,
                $resource->description
            ];
            
            if ($export->include_metadata) {
                $row = array_merge($row, [
                    $resource->created_at,
                    $resource->updated_at,
                    $resource->file_size,
                    $resource->file_type
                ]);
            }
            
            fputcsv($file, $row);
        }
        
        fclose($file);
        
        return [
            'success' => true,
            'file_path' => $csvPath,
            'file_size' => Storage::size($csvPath)
        ];
    }

    /**
     * 导出为XML格式
     */
    private function exportAsXml(ResourceExport $export, $resources, string $exportDir, string $filename): array
    {
        $xmlPath = $exportDir . '/' . $filename . '.xml';
        
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><resources></resources>');
        
        foreach ($resources as $resource) {
            $resourceNode = $xml->addChild('resource');
            $resourceNode->addChild('id', $resource->id);
            $resourceNode->addChild('name', htmlspecialchars($resource->name));
            $resourceNode->addChild('description', htmlspecialchars($resource->description));
            
            if ($export->include_metadata) {
                $metadataNode = $resourceNode->addChild('metadata');
                $metadataNode->addChild('created_at', $resource->created_at);
                $metadataNode->addChild('updated_at', $resource->updated_at);
                $metadataNode->addChild('file_size', $resource->file_size);
                $metadataNode->addChild('file_type', $resource->file_type);
            }
        }
        
        Storage::put($xmlPath, $xml->asXML());
        
        return [
            'success' => true,
            'file_path' => $xmlPath,
            'file_size' => Storage::size($xmlPath)
        ];
    }

    /**
     * 导出为PDF格式（简化实现）
     */
    private function exportAsPdf(ResourceExport $export, $resources, string $exportDir, string $filename): array
    {
        // 这里应该使用PDF库如TCPDF或DomPDF
        // 为了简化，这里先返回错误
        return [
            'success' => false,
            'error' => 'PDF导出功能暂未实现'
        ];
    }

    /**
     * 计算估算文件大小
     */
    private function calculateEstimatedSize($resources): int
    {
        $totalSize = 0;
        foreach ($resources as $resource) {
            $totalSize += $resource->file_size ?? 1024; // 默认1KB
        }
        return $totalSize;
    }

    /**
     * 计算估算处理时长
     */
    private function calculateEstimatedDuration(int $resourceCount): int
    {
        // 简单估算：每个资源需要1秒处理时间
        return max(1, $resourceCount);
    }

    /**
     * 解析大小字符串为字节数
     */
    private function parseSizeString(string $sizeStr): int
    {
        // 简化实现，实际应该解析如"1.5 MB"这样的字符串
        if (preg_match('/(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)/i', $sizeStr, $matches)) {
            $size = floatval($matches[1]);
            $unit = strtoupper($matches[2]);
            
            switch ($unit) {
                case 'GB':
                    return intval($size * 1024 * 1024 * 1024);
                case 'MB':
                    return intval($size * 1024 * 1024);
                case 'KB':
                    return intval($size * 1024);
                default:
                    return intval($size);
            }
        }
        
        return 0;
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize(int $bytes): string
    {
        if ($bytes >= 1024 * 1024 * 1024) {
            return round($bytes / (1024 * 1024 * 1024), 2) . ' GB';
        } elseif ($bytes >= 1024 * 1024) {
            return round($bytes / (1024 * 1024), 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}
