<?php

/** @var \Laravel\Lumen\Routing\Router $router */

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

$router->get('/', 'IndexController@index');

// API文档相关路由 - 支持动态加载架构
$router->get('/api-data', 'ApiDocumentController@getApiData');                    // 获取所有API数据
$router->get('/api-list', 'ApiDocumentController@getControllerList');             // 获取控制器列表
$router->get('/api-controller/{name}', 'ApiDocumentController@getControllerData'); // 获取单个控制器数据

$router->group(['prefix' => 'api'],function () use ($router){
    // 公开接口 - 按dev-api-guidelines-add.mdc标准
    $router->post('/login','Api\AuthController@login');
    $router->post('/register','Api\AuthController@register');

    // 需要认证的接口（使用 AuthService 控制器内置认证）
    $router->group([],function () use ($router){
        // 用户认证检查
        $router->get('/check', 'Api\AuthController@check');

        // 用户管理
        $router->get('/user/profile', 'Api\UserController@profile');
        $router->get('/user/preferences', 'Api\UserController@getPreferences');
        $router->put('/user/preferences', 'Api\UserController@updatePreferences');

        // 积分管理 - 🔧 LongChec2修复：将积分余额查询移入认证路由组
        $router->get('/points/balance', 'Api\PointsController@balance');
        $router->post('/points/recharge', 'Api\PointsController@recharge');
        $router->get('/points/transactions', 'Api\PointsController@transactions');

        // 风格管理
        $router->get('/styles/list', 'Api\StyleController@list');
        $router->get('/styles/popular', 'Api\StyleController@popular');
        $router->get('/styles/{id}', 'Api\StyleController@detail');

        // 项目管理 (静态路由必须在动态路由之前)
        $router->get('/projects/list', 'Api\ProjectController@list');
        $router->get('/projects/my-projects', 'Api\ProjectController@myProjects');
        $router->post('/projects/create', 'Api\ProjectController@create');
        $router->post('/projects/create-with-story', 'Api\ProjectController@createWithStory');
        $router->put('/projects/{id}/confirm-title', 'Api\ProjectController@confirmTitle');
        $router->put('/projects/{id}', 'Api\ProjectController@update');
        $router->delete('/projects/{id}', 'Api\ProjectController@delete');
        $router->get('/projects/{id}', 'Api\ProjectController@detail');
        $router->get('/projects/{id}', 'Api\ProjectController@show');

        // AI模型管理
        $router->get('/ai/models', 'Api\AiModelController@index');
        $router->post('/ai/models/switch', 'Api\AiModelController@switch');
        $router->get('/ai/models/performance', 'Api\AiModelController@performance');
        $router->get('/ai/service/health', 'Api\AiModelController@health');

        // AI生成服务
        $router->post('/ai/text/generate', 'Api\AiGenerationController@generateText');
        $router->get('/ai/tasks/{id}', 'Api\AiGenerationController@getTaskStatus');
        $router->get('/ai/tasks', 'Api\AiGenerationController@getUserTasks');
        $router->post('/ai/tasks/{id}/retry', 'Api\AiGenerationController@retryTask');

        // WebSocket管理
        $router->post('/websocket/auth', 'Api\WebSocketController@authenticate');
        $router->get('/websocket/sessions', 'Api\WebSocketController@getSessions');
        $router->post('/websocket/disconnect', 'Api\WebSocketController@disconnect');
        $router->get('/websocket/status', 'Api\WebSocketController@getStatus');

        // 角色管理 (静态路由必须在变量路由之前)
        $router->get('/characters/categories', 'Api\CharacterController@getCategories');
        $router->get('/characters/library', 'Api\CharacterController@getLibrary');
        $router->get('/characters/recommend', 'Api\CharacterController@getRecommendations');
        $router->get('/characters/my-bindings', 'Api\CharacterController@getMyBindings');
        $router->get('/characters/ranking', 'Api\CharacterController@ranking'); // 从第133行移动到此处
        $router->post('/characters/generate', 'Api\CharacterController@generate');
        $router->post('/characters/bind', 'Api\CharacterController@bindCharacter');
        $router->delete('/characters/unbind', 'Api\CharacterController@unbindCharacter');
        $router->put('/characters/bindings/{id}', 'Api\CharacterController@updateBinding');
        $router->get('/characters/{id}', 'Api\CharacterController@getCharacterDetail'); // 变量路由放在最后

        // 第2D1阶段：文本与图像生成接口 - 按dev-api-guidelines-add.mdc标准
        // 故事生成 (修正路径匹配规划文档)
        $router->post('/story/generate', 'Api\StoryController@generate');
        $router->post('/story/auto-save', 'Api\StoryController@autoSave');
        $router->post('/story/collaborate', 'Api\StoryController@collaborate');

        // 图像生成 (保留核心功能，移除不在规划文档中的接口)
        $router->post('/images/convert', 'Api\ImageController@convert');
        $router->post('/images/compress', 'Api\ImageController@compress');
        $router->post('/images/thumbnail', 'Api\ImageController@thumbnail');
        $router->put('/images/{id}/edit', 'Api\ImageController@edit');
        $router->post('/images/{id}/enhance', 'Api\ImageController@enhance');
        $router->post('/images/batch', 'Api\ImageController@batch');
        $router->get('/images/{id}/history', 'Api\ImageController@history');

        // 第2D2阶段：视频与音频生成接口 - 按dev-api-guidelines-add.mdc标准
        // 视频生成
        $router->post('/videos/generate', 'Api\VideoController@generate');
        $router->get('/videos/{id}/status', 'Api\VideoController@getStatus');
        $router->get('/videos/{id}/download', 'Api\VideoController@download');
        $router->post('/videos/storyboard', 'Api\VideoController@storyboard');
        $router->put('/videos/{id}/config', 'Api\VideoController@updateConfig');
        $router->post('/videos/{id}/cancel', 'Api\VideoController@cancel');

        // 语音合成 - 按dev-api-guidelines-add.mdc标准
        $router->get('/voices', 'Api\VoiceController@index');
        $router->post('/voices/synthesize', 'Api\VoiceController@synthesize');
        $router->get('/voices/{id}/preview', 'Api\VoiceController@preview');
        $router->post('/voices/{id}/favorite', 'Api\VoiceController@favorite');
        $router->get('/voices/{id}', 'Api\VoiceController@show');

        // 任务管理
        $router->post('/tasks/{id}/cancel', 'Api\TaskManagementController@cancelTask');
        $router->post('/tasks/{id}/retry', 'Api\TaskManagementController@retryTask');
        $router->get('/tasks/timeout-config', 'Api\TaskManagementController@getTimeoutConfig');
        $router->get('/tasks/{id}/recovery', 'Api\TaskManagementController@getRecoveryStatus');
        $router->get('/batch/tasks/status', 'Api\TaskManagementController@getBatchStatus');

        // 角色管理扩展接口 - 按dev-api-guidelines-add.mdc标准新增
        $router->get('/characters', 'Api\CharacterController@index');
        $router->post('/characters', 'Api\CharacterController@store');
        $router->put('/characters/{id}', 'Api\CharacterController@update');
        $router->delete('/characters/{id}', 'Api\CharacterController@destroy');
        $router->post('/characters/ai-expand', 'Api\CharacterController@aiExpand');
        // ranking路由已移动到第83行
        $router->post('/characters/{id}/generate-image', 'Api\CharacterController@generateImage');
        $router->post('/characters/feedback', 'Api\CharacterController@submitFeedback');

        // 文件管理
        $router->post('/files/upload', 'Api\FileController@upload');
        $router->get('/files/list', 'Api\FileController@getFiles');
        $router->get('/files/{id}', 'Api\FileController@getFileDetail');
        $router->delete('/files/{id}', 'Api\FileController@deleteFile');
        $router->get('/files/{id}/download', 'Api\FileController@downloadFile');



        // 系统管理 (移除SystemController冲突路由，统一使用SystemMonitorController)
        $router->get('/system/search', 'Api\SystemController@globalSearch');

        // 第2D3阶段：音乐音效音色生成接口
        // 音乐生成模块
        $router->post('/music/generate', 'Api\MusicController@generate');
        $router->get('/music/{id}/status', 'Api\MusicController@getStatus');
        $router->get('/music/{id}/result', 'Api\MusicController@getResult');
        $router->post('/batch/music/generate', 'Api\MusicController@batchGenerate');

        // 音效生成模块
        $router->post('/sounds/generate', 'Api\SoundController@generate');
        $router->get('/sounds/{id}/status', 'Api\SoundController@getStatus');
        $router->get('/sounds/{id}/result', 'Api\SoundController@getResult');
        $router->post('/batch/sounds/generate', 'Api\SoundController@batchGenerate');

        // 音色生成模块（扩展现有VoiceController）
        $router->post('/voices/clone', 'Api\VoiceController@clone');
        $router->get('/voices/clone/{id}/status', 'Api\VoiceController@getCloneStatus');
        $router->post('/voices/custom', 'Api\VoiceController@custom');
        $router->get('/voices/custom/{id}/status', 'Api\VoiceController@getCustomStatus');

        // 音频处理模块
        $router->post('/audio/mix', 'Api\AudioController@mix');
        $router->get('/audio/mix/{id}/status', 'Api\AudioController@getMixStatus');
        $router->post('/audio/enhance', 'Api\AudioController@enhance');
        $router->get('/audio/enhance/{id}/status', 'Api\AudioController@getEnhanceStatus');

        // 广告管理
        $router->post('/ad.store', 'Api\AdController@ad_store');
        $router->post('/ad.update', 'Api\AdController@ad_update');

        // 第2E阶段：积分管理API接口
        $router->post('/credits/check', 'Api\CreditsController@checkCredits');
        $router->post('/credits/freeze', 'Api\CreditsController@freezeCredits');
        $router->post('/credits/refund', 'Api\CreditsController@refundCredits');

        // 第2G阶段：性能监控接口
        $router->get('/system/health', 'Api\SystemMonitorController@health');
        $router->get('/system/metrics', 'Api\SystemMonitorController@metrics');
        $router->get('/system/response-time', 'Api\SystemMonitorController@responseTime');

        // 第3A阶段：资源管理 - 按dev-api-guidelines-add.mdc标准
        // 资源管理模块
        $router->get('/resources/{id}/download-info', 'Api\ResourceController@getDownloadInfo');
        $router->post('/resources/{id}/confirm-download', 'Api\ResourceController@confirmDownload');
        $router->get('/resources/my-resources', 'Api\ResourceController@myResources');
        $router->put('/resources/{id}/status', 'Api\ResourceController@updateStatus');

        // 版本控制系统模块
        $router->post('/resources/{id}/versions', 'Api\VersionController@create');
        $router->get('/resources/{id}/versions', 'Api\VersionController@list');
        $router->get('/versions/compare', 'Api\VersionController@compare');
        $router->get('/versions/{id}', 'Api\VersionController@show');
        $router->put('/versions/{id}/set-current', 'Api\VersionController@setCurrent');
        $router->delete('/versions/{id}', 'Api\VersionController@delete');

        // WebSocket认证模块 - 按dev-api-guidelines-add.mdc标准新增 (移除重复路由)

        // Analytics模块
        $router->get('/analytics/user-behavior', 'Api\AnalyticsController@getUserBehavior');
        $router->get('/analytics/system-usage', 'Api\AnalyticsController@getSystemUsage');
        $router->get('/analytics/ai-performance', 'Api\AnalyticsController@getAiPerformance');
        $router->get('/analytics/revenue', 'Api\AnalyticsController@getRevenue');

        // 缺失的路由 - 修复500错误
        // 系统监控模块
        $router->get('/system/overview', 'Api\SystemController@overview');

        // 用户管理模块
        $router->put('/user/profile', 'Api\UserController@updateProfile');



        // 风格管理模块
        $router->post('/styles/create', 'Api\StyleController@create');

        // 素材管理模块 - 按dev-api-guidelines-add.mdc标准
        $router->get('/assets', 'Api\AssetController@index');
        $router->post('/assets/organize', 'Api\AssetController@organize');
        $router->post('/assets/upload', 'Api\AssetController@upload');
        $router->get('/assets/{id}', 'Api\AssetController@show');
        $router->put('/assets/{id}', 'Api\AssetController@update');
        $router->delete('/assets/{id}', 'Api\AssetController@delete');

        // 用户成长模块 - 按dev-api-guidelines-add.mdc标准
        $router->get('/user/growth-path', 'Api\UserController@getGrowthPath');
        $router->post('/user/milestone', 'Api\UserController@recordMilestone');

        // 使用统计模块 - 按dev-api-guidelines-add.mdc标准
        $router->get('/usage/stats', 'Api\UsageController@getStats');
        $router->get('/usage/trends', 'Api\UsageController@getTrends');
        $router->post('/usage/export', 'Api\UsageController@exportData');
        $router->get('/usage/quota', 'Api\UsageController@getQuota');

        // 个性化推荐模块 - 按dev-api-guidelines-add.mdc标准
        $router->get('/recommendations/features', 'Api\RecommendationController@getFeatures');
        $router->post('/recommendations/feedback', 'Api\RecommendationController@submitFeedback');

        // AI模型管理模块 - 按dev-api-guidelines-add.mdc标准
        $router->get('/ai/models/performance', 'Api\AiModelController@getPerformance');
        $router->post('/ai/models/switch', 'Api\AiModelController@switchModel');

        // 配置管理模块
        $router->post('/config/validate', 'Api\ConfigController@validateConfig');
    });

    // 🔧 作品发布模块 - 严格按照dev-api-guidelines-add.mdc规范实现
    $router->group(['prefix' => 'works'], function () use ($router) {
        $router->post('/publish', 'Api\WorkPublishController@publishWork');           // 发布作品
        $router->put('/{id}', 'Api\WorkPublishController@updateWork');               // 编辑作品
        $router->delete('/{id}', 'Api\WorkPublishController@deleteWork');            // 删除作品
        $router->get('/my-works', 'Api\WorkPublishController@getMyWorks');           // 我的作品
        $router->get('/gallery', 'Api\WorkPublishController@getGallery');            // 作品展示库
        $router->get('/{id}/share', 'Api\WorkPublishController@getShareLink');       // 获取分享链接
        $router->post('/{id}/like', 'Api\WorkPublishController@likeWork');           // 点赞作品
        $router->get('/trending', 'Api\WorkPublishController@getTrendingWorks');     // 热门作品
        $router->post('/publish-style', 'Api\WorkPublishController@publishStyle');   // 发布风格作品
        $router->post('/publish-character', 'Api\WorkPublishController@publishCharacter'); // 发布角色作品
        $router->post('/publish-video', 'Api\WorkPublishController@publishVideo');   // 发布视频作品
    });

    // 🔧 移除Publications模块 - 不在dev-api-guidelines-add.mdc规划文档中
    // 所有Publications功能已迁移到Works模块
});

