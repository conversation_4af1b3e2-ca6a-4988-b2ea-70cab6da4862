#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
找出 apitest-code.mdc 中遗漏的接口
"""

import re

def extract_apis_from_final(file_path):
    """从 apitest-final.mdc 提取所有API"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 匹配接口定义
    pattern = r'#### 步骤\d+: (\d+\.\d+) (.+?) (GET|POST|PUT|DELETE) (/api/[^\s]+)'
    matches = re.findall(pattern, content)
    
    apis = []
    for api_num, name, method, path in matches:
        apis.append({
            'api_num': api_num,
            'name': name,
            'method': method,
            'path': path,
            'full_api': f"{method} {path}"
        })
    
    return apis

def extract_apis_from_code(file_path):
    """从 apitest-code.mdc 提取所有API"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 匹配格式: - [ ] **X.X** 接口名称 `METHOD /api/path`
    pattern = r'- \[ \] \*\*(\d+\.\d+)\*\* (.+?) `(GET|POST|PUT|DELETE) (/api/[^\s`]+)`'
    matches = re.findall(pattern, content)
    
    apis = []
    for api_num, name, method, path in matches:
        apis.append({
            'api_num': api_num,
            'name': name,
            'method': method,
            'path': path,
            'full_api': f"{method} {path}"
        })
    
    return apis

def find_missing_apis():
    """找出遗漏的API"""
    
    print("正在分析 apitest-final.mdc...")
    final_apis = extract_apis_from_final("apitest-final.mdc")
    
    print("正在分析 apitest-code.mdc...")
    code_apis = extract_apis_from_code("apitest-code.mdc")
    
    print(f"apitest-final.mdc 中的API数量: {len(final_apis)}")
    print(f"apitest-code.mdc 中的API数量: {len(code_apis)}")
    
    # 创建集合用于比较
    final_api_set = set(api['full_api'] for api in final_apis)
    code_api_set = set(api['full_api'] for api in code_apis)
    
    # 找出遗漏的API
    missing_api_paths = final_api_set - code_api_set
    
    print(f"\n遗漏的API数量: {len(missing_api_paths)}")
    
    # 找出遗漏的完整API信息
    missing_apis = []
    for api in final_apis:
        if api['full_api'] in missing_api_paths:
            missing_apis.append(api)
    
    # 按接口编号排序
    missing_apis.sort(key=lambda x: float(x['api_num']))
    
    print("\n遗漏的API列表:")
    print("=" * 80)
    
    for api in missing_apis:
        print(f"- [ ] **{api['api_num']}** {api['name']} `{api['method']} {api['path']}`")
    
    return missing_apis

if __name__ == "__main__":
    missing_apis = find_missing_apis()
    
    # 保存遗漏的API到文件
    with open("missing_apis.txt", "w", encoding="utf-8") as f:
        for api in missing_apis:
            f.write(f"- [ ] **{api['api_num']}** {api['name']} `{api['method']} {api['path']}`\n")
    
    print(f"\n遗漏的API已保存到 missing_apis.txt 文件中")
