<?php

/** @var \Laravel\Lumen\Routing\Router $router */

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

$router->get('/', 'IndexController@index');

// API文档相关路由 - 支持动态加载架构
$router->get('/api-data', 'ApiDocumentController@getApiData');                    // 获取所有API数据
$router->get('/api-list', 'ApiDocumentController@getControllerList');             // 获取控制器列表
$router->get('/api-controller/{name}', 'ApiDocumentController@getControllerData'); // 获取单个控制器数据

// ==================== 阶段1：公开路由（无需认证）====================
$router->group(['prefix' => 'api'], function () use ($router) {
    // 用户认证相关路由（已存在）
    $router->post('/login', 'Api\AuthController@login');
    $router->post('/register', 'Api\AuthController@register');
    $router->get('/check', 'Api\AuthController@check');

    // 验证码相关路由（公开访问）
    $router->get('/captcha/generate', 'Api\CaptchaController@generate');
    $router->post('/captcha/verify', 'Api\CaptchaController@verify');
    $router->post('/captcha/refresh', 'Api\CaptchaController@refresh');

    // 缓存信息路由（公开访问）
    $router->get('/cache/stats', 'Api\CacheController@getStats');
    $router->get('/cache/keys', 'Api\CacheController@getKeys');
    $router->get('/cache/get', 'Api\CacheController@getValue');
    $router->get('/cache/config', 'Api\CacheController@getConfig');

    // 风格信息路由（公开访问）
    $router->get('/styles/list', 'Api\StyleController@list');
    $router->get('/styles/popular', 'Api\StyleController@popular');
    $router->get('/styles/{id}', 'Api\StyleController@detail');
});

// ==================== 阶段2：认证路由（需要登录）====================
$router->group(['prefix' => 'api', 'middleware' => 'auth'], function () use ($router) {

    // ========== 用户管理相关路由 ==========
    $router->get('/user/profile', 'Api\UserController@profile');
    $router->put('/user/profile', 'Api\UserController@updateProfile');
    $router->get('/user/preferences', 'Api\UserController@getPreferences');
    $router->put('/user/preferences', 'Api\UserController@updatePreferences');

    // ========== 积分管理相关路由 ==========
    $router->get('/points/balance', 'Api\PointsController@balance');
    $router->post('/points/recharge', 'Api\PointsController@recharge');
    $router->get('/points/transactions', 'Api\PointsController@transactions');

    // 积分高级管理
    $router->post('/credits/check', 'Api\CreditsController@checkCredits');
    $router->post('/credits/freeze', 'Api\CreditsController@freezeCredits');
    $router->post('/credits/refund', 'Api\CreditsController@refundCredits');

    // ========== AI模型管理相关路由 ==========
    $router->get('/ai-models/available', 'Api\AiModelController@available');
    $router->get('/ai-models/favorites', 'Api\AiModelController@favorites');
    $router->get('/ai-models/list', 'Api\AiModelController@list');
    $router->post('/ai-models/switch', 'Api\AiModelController@switch');
    $router->get('/ai-models/usage-stats', 'Api\AiModelController@usageStats');
    $router->get('/ai-models/{model_id}/detail', 'Api\AiModelController@detail');
    $router->post('/ai-models/{model_id}/test', 'Api\AiModelController@test');
    $router->post('/ai-models/{model_id}/favorite', 'Api\AiModelController@favorite');

    // 🔧 新增：智能平台选择和健康监控路由 
    $router->post('/ai-models/select-platform', 'Api\AiModelController@selectOptimalPlatform');
    $router->get('/ai-models/platforms-health', 'Api\AiModelController@getAllPlatformsHealth');
    $router->get('/ai-models/platform-health/{platform}', 'Api\AiModelController@checkPlatformHealth');
    $router->get('/ai-models/platform-stats/{platform}', 'Api\AiModelController@getPlatformStats');

    // ========== 素材管理相关路由 ==========
    $router->get('/assets/list', 'Api\AssetController@list');
    $router->post('/assets/upload', 'Api\AssetController@upload');
    $router->get('/assets/{id}', 'Api\AssetController@show');
    $router->delete('/assets/{id}', 'Api\AssetController@delete');

    // ========== 缓存管理相关路由 ==========
    $router->delete('/cache/clear', 'Api\CacheController@clearCache');
    $router->post('/cache/warmup', 'Api\CacheController@warmupCache');
    $router->put('/cache/set', 'Api\CacheController@setValue');
    $router->delete('/cache/delete', 'Api\CacheController@deleteKeys');

    // 角色库管理
    $router->get('/characters/categories', 'Api\CharacterController@getCategories');
    $router->get('/characters/list', 'Api\CharacterController@getLibrary');
    $router->get('/characters/recommendations', 'Api\CharacterController@getRecommendations');
    $router->get('/characters/bindings', 'Api\CharacterController@getMyBindings');
    $router->post('/characters/bind', 'Api\CharacterController@bindCharacter');
    $router->delete('/characters/unbind', 'Api\CharacterController@unbindCharacter');
    $router->post('/characters/generate', 'Api\CharacterController@generate');
    $router->put('/characters/bindings/{id}', 'Api\CharacterController@updateBinding');
    $router->get('/characters/{id}', 'Api\CharacterController@getCharacterDetail');

    // ========== 配置管理相关路由 ==========
    $router->get('/config/system', 'Api\ConfigController@getSystemConfig');
    $router->put('/config/system', 'Api\ConfigController@updateSystemConfig');
    $router->get('/config/user', 'Api\ConfigController@getUserConfig');
    $router->put('/config/user', 'Api\ConfigController@updateUserConfig');
    $router->get('/config/ai', 'Api\ConfigController@getAiConfig');
    $router->put('/config/ai', 'Api\ConfigController@updateAiConfig');
    $router->post('/config/reset', 'Api\ConfigController@resetConfig');

    // ========== 通知管理相关路由 ==========
    $router->get('/notifications', 'Api\NotificationController@index');
    $router->put('/notifications/mark-read', 'Api\NotificationController@markAsRead');
    $router->put('/notifications/mark-all-read', 'Api\NotificationController@markAllAsRead');
    $router->get('/notifications/stats', 'Api\NotificationController@stats');
    $router->post('/notifications/send', 'Api\NotificationController@send');
    $router->delete('/notifications/{id}', 'Api\NotificationController@destroy');

    // ========== 模板管理相关路由 ==========
    $router->post('/templates/create', 'Api\TemplateController@create');
    $router->get('/templates/marketplace', 'Api\TemplateController@marketplace');
    $router->get('/templates/my-templates', 'Api\TemplateController@myTemplates');
    $router->post('/templates/{id}/use', 'Api\TemplateController@use');
    $router->get('/templates/{id}/detail', 'Api\TemplateController@detail');
    $router->put('/templates/{id}', 'Api\TemplateController@update');
    $router->delete('/templates/{id}', 'Api\TemplateController@delete');

    // ========== 统一任务管理相关路由(整合了TaskManagementController和AiTaskController功能) ==========
    $router->get('/tasks', 'Api\AiTaskController@index');                       // 获取任务列表
    $router->get('/tasks/{id}', 'Api\AiTaskController@show');                   // 获取任务详情
    $router->get('/tasks/stats', 'Api\AiTaskController@stats');                 // 获取任务统计
    $router->get('/tasks/timeout-config', 'Api\AiTaskController@timeoutConfig'); // 获取超时配置
    $router->post('/tasks/{id}/cancel', 'Api\AiTaskController@cancel');         // 取消任务
    $router->post('/tasks/{id}/retry', 'Api\AiTaskController@retry');           // 重试任务
    $router->get('/tasks/batch/status', 'Api\AiTaskController@batchStatus');    // 批量查询任务状态
    $router->get('/tasks/{id}/recovery', 'Api\AiTaskController@recovery');      // 查询任务恢复状态

    // ========== 版本管理相关路由 ==========
    $router->get('/versions/compare', 'Api\VersionController@compare');
    $router->post('/resources/{id}/versions', 'Api\VersionController@create');
    $router->get('/resources/{id}/versions', 'Api\VersionController@list');
    $router->get('/versions/{id}', 'Api\VersionController@show');
    $router->put('/versions/{id}/set-current', 'Api\VersionController@setCurrent');
    $router->delete('/versions/{id}', 'Api\VersionController@delete');

    // ========== 下载管理相关路由 ==========
    $router->get('/downloads/list', 'Api\DownloadController@list');
    $router->get('/downloads/statistics', 'Api\DownloadController@statistics');
    $router->post('/downloads/create-link', 'Api\DownloadController@createLink');
    $router->post('/downloads/batch', 'Api\DownloadController@batchDownload');
    $router->post('/downloads/cleanup', 'Api\DownloadController@cleanup');
    $router->get('/downloads/secure/{token}', 'Api\DownloadController@secureDownload');
    $router->post('/downloads/{id}/retry', 'Api\DownloadController@retry');

    // ========== 文件管理相关路由 ==========
    $router->post('/files/upload', 'Api\FileController@upload');
    $router->get('/files/list', 'Api\FileController@getFiles');
    $router->get('/files/{id}', 'Api\FileController@getFileDetail');
    $router->delete('/files/{id}', 'Api\FileController@deleteFile');
    $router->get('/files/{id}/download', 'Api\FileController@downloadFile');

    // ========== 日志管理相关路由 ==========
    $router->get('/logs/system', 'Api\LogController@systemLogs');
    $router->get('/logs/user-actions', 'Api\LogController@userActionLogs');
    $router->get('/logs/ai-calls', 'Api\LogController@aiCallLogs');
    $router->get('/logs/errors', 'Api\LogController@errorLogs');
    $router->post('/logs/export', 'Api\LogController@exportLogs');
    $router->put('/logs/errors/{id}/resolve', 'Api\LogController@resolveError');

    // ========== 项目管理相关路由 ==========
    // 基础项目管理（ProjectController - 专注内容创作项目）
    $router->post('/projects/create-with-story', 'Api\ProjectController@createWithStory');
    $router->get('/projects/my-projects', 'Api\ProjectController@myProjects');
    $router->get('/projects/list', 'Api\ProjectController@list');
    $router->post('/projects/create', 'Api\ProjectController@create');
    $router->put('/projects/{id}/confirm-title', 'Api\ProjectController@confirmTitle');
    $router->get('/projects/{id}', 'Api\ProjectController@detail');
    $router->put('/projects/{id}', 'Api\ProjectController@update');
    $router->delete('/projects/{id}', 'Api\ProjectController@delete');

    // 高级项目管理（ProjectManagementController - 专注项目管理功能）
    $router->post('/project-management/tasks', 'Api\ProjectManagementController@createTask');
    $router->get('/project-management/progress', 'Api\ProjectManagementController@getProgress');
    $router->post('/project-management/assign-resources', 'Api\ProjectManagementController@assignResources');
    $router->get('/project-management/statistics', 'Api\ProjectManagementController@getStatistics');
    $router->post('/project-management/collaborate', 'Api\ProjectManagementController@collaborate');
    $router->get('/project-management/milestones', 'Api\ProjectManagementController@getMilestones');

    // ========== AI生成相关路由(专注AI任务的创建和执行，向第三方AI平台（虚拟AI API服务）发起请求) ==========
    // 文本生成
    $router->post('/ai/text/generate', 'Api\AiGenerationController@generateText');
    $router->get('/ai/tasks', 'Api\AiGenerationController@getUserTasks');
    $router->get('/ai/tasks/{id}', 'Api\AiGenerationController@getTaskStatus');
    $router->post('/ai/tasks/{id}/retry', 'Api\AiGenerationController@retryTask');

    // 图像生成
    $router->post('/images/generate', 'Api\ImageController@generate');
    $router->post('/images/batch-generate', 'Api\ImageController@batchGenerate');
    $router->get('/images/{id}/status', 'Api\ImageController@getStatus');
    $router->get('/images/{id}/result', 'Api\ImageController@getResult');

    // 音频处理
    $router->post('/audio/mix', 'Api\AudioController@mix');
    $router->post('/audio/enhance', 'Api\AudioController@enhance');
    $router->get('/audio/mix/{id}/status', 'Api\AudioController@getMixStatus');
    $router->get('/audio/enhance/{id}/status', 'Api\AudioController@getEnhanceStatus');

    // 音乐生成
    $router->post('/music/generate', 'Api\MusicController@generate');
    $router->post('/music/batch-generate', 'Api\MusicController@batchGenerate');
    $router->get('/music/{id}/status', 'Api\MusicController@getStatus');
    $router->get('/music/{id}/result', 'Api\MusicController@getResult');

    // 声音生成
    $router->post('/sounds/generate', 'Api\SoundController@generate');
    $router->post('/sounds/batch-generate', 'Api\SoundController@batchGenerate');
    $router->get('/sounds/{id}/status', 'Api\SoundController@getStatus');
    $router->get('/sounds/{id}/result', 'Api\SoundController@getResult');

    // 视频生成
    $router->post('/videos/generate', 'Api\VideoController@generate');
    $router->get('/videos/{id}/status', 'Api\VideoController@getStatus');
    $router->get('/videos/{id}/result', 'Api\VideoController@getResult');

    // 语音合成
    $router->post('/voice/synthesize', 'Api\VoiceController@synthesize');
    $router->post('/voice/batch-synthesize', 'Api\VoiceController@batchSynthesize');
    $router->post('/voice/clone', 'Api\VoiceController@clone');
    $router->post('/voice/custom', 'Api\VoiceController@custom');
    $router->post('/voice/preview', 'Api\VoiceController@preview');
    $router->get('/voice/custom/{id}/status', 'Api\VoiceController@getCustomStatus');
    $router->get('/voice/clone/{id}/status', 'Api\VoiceController@getCloneStatus');
    $router->get('/voice/{id}/status', 'Api\VoiceController@getStatus');

    // ========== 故事生成相关路由 ==========
    $router->post('/stories/generate', 'Api\StoryController@generate');
    $router->get('/stories/{id}/status', 'Api\StoryController@getStatus');

    // ========== 资源管理相关路由 ==========
    $router->post('/resources/generate', 'Api\ResourceController@generate');
    $router->get('/resources/list', 'Api\ResourceController@list');
    $router->get('/resources/my-resources', 'Api\ResourceController@myResources');
    $router->post('/resources/batch-generate', 'Api\ResourceController@batchGenerate');
    $router->put('/resources/{id}/status', 'Api\ResourceController@updateStatus');
    $router->get('/resources/{id}/status', 'Api\ResourceController@getStatus');
    $router->get('/resources/{id}/download-info', 'Api\ResourceController@getDownloadInfo');
    $router->post('/resources/{id}/confirm-download', 'Api\ResourceController@confirmDownload');
    $router->delete('/resources/{id}', 'Api\ResourceController@delete');

    // ========== 发布管理相关路由 ==========
    $router->post('/publications/publish', 'Api\PublicationController@publish');
    $router->get('/publications/my-publications', 'Api\PublicationController@myPublications');
    $router->get('/publications/plaza', 'Api\PublicationController@plaza');
    $router->get('/publications/trending', 'Api\PublicationController@trending');
    $router->get('/publications/{id}/status', 'Api\PublicationController@getStatus');
    $router->post('/publications/{id}/unpublish', 'Api\PublicationController@unpublish');
    $router->get('/publications/{id}/detail', 'Api\PublicationController@detail');
    $router->put('/publications/{id}', 'Api\PublicationController@update');
    $router->delete('/publications/{id}', 'Api\PublicationController@delete');

    // ========== 推荐系统相关路由 ==========
    $router->get('/recommendations/content', 'Api\RecommendationController@getContentRecommendations');
    $router->get('/recommendations/users', 'Api\RecommendationController@getUserRecommendations');
    $router->get('/recommendations/topics', 'Api\RecommendationController@getTopicRecommendations');
    $router->post('/recommendations/feedback', 'Api\RecommendationController@submitFeedback');
    $router->get('/recommendations/preferences', 'Api\RecommendationController@getPreferences');
    $router->put('/recommendations/preferences', 'Api\RecommendationController@updatePreferences');
    $router->get('/recommendations/analytics', 'Api\RecommendationController@getAnalytics');
    $router->get('/recommendations/personalized', 'Api\RecommendationController@getPersonalizedRecommendations');

    // ========== 审核管理相关路由 ==========
    $router->post('/reviews/submit', 'Api\ReviewController@submit');
    $router->get('/reviews/my-reviews', 'Api\ReviewController@myReviews');
    $router->get('/reviews/queue-status', 'Api\ReviewController@queueStatus');
    $router->get('/reviews/guidelines', 'Api\ReviewController@guidelines');
    $router->post('/reviews/pre-check', 'Api\ReviewController@preCheck');
    $router->get('/reviews/{id}/status', 'Api\ReviewController@getStatus');
    $router->post('/reviews/{id}/appeal', 'Api\ReviewController@appeal');

    // ========== 社交功能相关路由 ==========
    $router->post('/social/follow', 'Api\SocialController@follow');
    $router->get('/social/follows', 'Api\SocialController@getFollows');
    $router->post('/social/like', 'Api\SocialController@like');
    $router->post('/social/comment', 'Api\SocialController@comment');
    $router->post('/social/share', 'Api\SocialController@share');
    $router->get('/social/feed', 'Api\SocialController@getFeed');
    $router->get('/social/notifications', 'Api\SocialController@getNotifications');
    $router->put('/social/notifications/read', 'Api\SocialController@markNotificationsRead');
    $router->get('/social/stats', 'Api\SocialController@getStats');
    $router->get('/social/{id}/comments', 'Api\SocialController@getComments');

    // ========== 作品发布相关路由 ==========
    $router->post('/works/publish', 'Api\WorkPublishController@publishWork');
    $router->get('/works/my-works', 'Api\WorkPublishController@myWorks');
    $router->get('/works/gallery', 'Api\WorkPublishController@gallery');
    $router->get('/works/trending', 'Api\WorkPublishController@trending');
    $router->get('/works/{id}/share-link', 'Api\WorkPublishController@getShareLink');
    $router->post('/works/{id}/like', 'Api\WorkPublishController@like');
    $router->put('/works/{id}', 'Api\WorkPublishController@update');
    $router->delete('/works/{id}', 'Api\WorkPublishController@delete');

    // ========== 工作流管理相关路由 ==========
    $router->post('/workflows/create', 'Api\WorkflowController@create');
    $router->get('/workflows', 'Api\WorkflowController@index');
    $router->post('/workflows/{id}/execute', 'Api\WorkflowController@execute');
    $router->get('/workflows/{id}/execution-status', 'Api\WorkflowController@getExecutionStatus');
    $router->post('/workflows/{id}/step-input', 'Api\WorkflowController@provideStepInput');
    $router->post('/workflows/{id}/cancel', 'Api\WorkflowController@cancelExecution');
    $router->get('/workflows/{id}/history', 'Api\WorkflowController@getExecutionHistory');
    $router->get('/workflows/{id}', 'Api\WorkflowController@show');

    // ========== 用户成长相关路由 ==========
    $router->get('/user-growth/profile', 'Api\UserGrowthController@getProfile');
    $router->get('/user-growth/leaderboard', 'Api\UserGrowthController@getLeaderboard');
    $router->get('/user-growth/daily-tasks', 'Api\UserGrowthController@getDailyTasks');
    $router->get('/user-growth/history', 'Api\UserGrowthController@getHistory');
    $router->get('/user-growth/statistics', 'Api\UserGrowthController@getStatistics');
    $router->post('/user-growth/goals', 'Api\UserGrowthController@setGoals');
    $router->get('/user-growth/recommendations', 'Api\UserGrowthController@getRecommendations');
    $router->get('/user-growth/milestones', 'Api\UserGrowthController@getMilestones');
    $router->post('/user-growth/daily-tasks/{id}/complete', 'Api\UserGrowthController@completeDailyTask');
    $router->post('/user-growth/achievements/{id}/complete', 'Api\UserGrowthController@completeAchievement');

    // ========== 风格创建相关路由（认证后访问）==========
    $router->post('/styles/create', 'Api\StyleController@create');
});

// ==================== 阶段3：管理路由（需要管理员权限）====================
$router->group(['prefix' => 'api', 'middleware' => ['auth', 'admin']], function () use ($router) {

    // ========== 权限管理相关路由 ==========
    $router->post('/permissions/check', 'Api\PermissionController@checkPermission');
    $router->get('/permissions/roles', 'Api\PermissionController@getRoles');
    $router->put('/permissions/assign-role', 'Api\PermissionController@assignRole');
    $router->post('/permissions/grant', 'Api\PermissionController@grantPermission');
    $router->delete('/permissions/revoke', 'Api\PermissionController@revokePermission');
    $router->get('/permissions/history', 'Api\PermissionController@getPermissionHistory');
    $router->get('/permissions/user/{id}', 'Api\PermissionController@getUserPermissions');

    // ========== 系统分析相关路由 ==========
    $router->get('/analytics/user-behavior', 'Api\AnalyticsController@getUserBehaviorAnalytics');
    $router->get('/analytics/system-usage', 'Api\AnalyticsController@getSystemUsage');
    $router->get('/analytics/ai-performance', 'Api\AnalyticsController@getAiPerformanceAnalytics');
    $router->get('/analytics/user-retention', 'Api\AnalyticsController@getUserRetentionAnalytics');
    $router->get('/analytics/revenue', 'Api\AnalyticsController@getRevenueAnalytics');
    $router->post('/analytics/custom-report', 'Api\AnalyticsController@generateCustomReport');

    // ========== 广告管理相关路由 ==========
    $router->get('/ads/list', 'Api\AdController@list');
    $router->post('/ads/create', 'Api\AdController@create');
});

// ==================== 阶段4：特殊路由处理 ====================
// WebSocket认证路由（特殊处理）
$router->group(['prefix' => 'api'], function () use ($router) {
    $router->post('/websocket/auth', 'Api\WebSocketController@authenticate');
    $router->get('/websocket/sessions', 'Api\WebSocketController@getSessions');
    $router->delete('/websocket/disconnect', 'Api\WebSocketController@disconnect');
    $router->get('/websocket/status', 'Api\WebSocketController@getStatus');
});

// 批量操作路由（统一管理）
$router->group(['prefix' => 'api/batch', 'middleware' => 'auth'], function () use ($router) {
    $router->post('/images/generate', 'Api\BatchController@generateImages');
    $router->post('/voices/synthesize', 'Api\BatchController@synthesizeVoices');
    $router->post('/music/generate', 'Api\BatchController@generateMusic');
    $router->get('/tasks/status', 'Api\AiTaskController@batchStatus');
    $router->post('/tasks/cancel', 'Api\BatchController@cancelBatch');
});

