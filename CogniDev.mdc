# CogniDev 执行报告

## 任务接管
接管来自 @CogniArch 的API接口差异化检测任务。

## 执行计划

### 第一步：系统性提取文档API接口
从 apitest-code.mdc 中提取所有API接口，使用正则表达式匹配格式：
`- [ ] **X.X** 接口名称 \`HTTP方法 /api/路径\``

### 第二步：提取实际路由定义
从 php/api/routes/web.php 中提取所有实际定义的API路由

### 第三步：差异化分析
对比两个数据集，找出：
1. 文档中独立存在的API接口
2. 控制器中独立存在的API接口

### 第四步：按格式输出结果
按照用户指定的汇报规范输出结果

## 执行状态
- [x] 接管任务
- [x] 提取文档API接口
- [x] 提取实际路由定义
- [x] 执行差异化分析
- [x] 生成最终报告

## 分析结果

### 汇报规范
按照用户指定的汇报规范输出：

**检测数量：文档api接口(275)个，实际存在控制器的api接口(263)个**

### apitest-code.mdc中独立存在的api接口：
6 POST /api/ad/store
10 POST /api/ad/update
36 GET /api/ai-models/platform-comparison
40 GET /api/ai-models/business-platforms
68 POST /api/logout
72 POST /api/refresh
76 POST /api/forgot-password
80 POST /api/reset-password
84 GET /api/verify
130 POST /api/websocket/disconnect
166 POST /api/tasks
226 GET /api/monitor/metrics
230 GET /api/monitor/realtime
234 GET /api/monitor/alerts
238 PUT /api/monitor/alerts/{id}/acknowledge
242 PUT /api/monitor/alerts/{id}/resolve
463 GET /api/permissions/user
561 POST /api/ai/generate
594 POST /api/batch/images/generate
598 POST /api/batch/voices/synthesize
602 POST /api/batch/music/generate
606 GET /api/batch/{batch_id}/status
610 DELETE /api/batch/{batch_id}
720 GET /api/images/{task_id}/status
724 POST /api/images/{task_id}/switch-platform
736 GET /api/images/history
772 GET /api/music/{task_id}/status
776 GET /api/music/styles
784 POST /api/batch/music/generate
954 POST /api/batch/resources/generate
1006 GET /api/social/comments
1022 POST /api/social/mark-notifications-read
1044 POST /api/batch/sounds/generate
1054 GET /api/videos/{task_id}/status
1058 GET /api/videos/platform-comparison
1068 POST /api/voices/synthesize
1072 GET /api/voices/{task_id}/status
1076 GET /api/voices/platform-comparison
1080 POST /api/voices/batch-synthesize
1084 POST /api/voices/clone
1088 GET /api/voices/clone/{id}/status
1092 POST /api/voices/custom
1096 GET /api/voices/custom/{id}/status
1100 POST /api/voices/{id}/preview
1104 GET /api/voices/history
1130 GET /api/works/{id}/share
1144 POST /api/workflows
1160 GET /api/workflows/executions/{execution_id}
1164 POST /api/workflows/executions/{execution_id}/input
1168 DELETE /api/workflows/executions/{execution_id}
1172 GET /api/workflows/{id}/executions
1186 POST /api/user-growth/complete-achievement
1194 POST /api/user-growth/complete-daily-task
1206 POST /api/user-growth/set-goals

**文档中独立存在的接口总数：53个**

### 控制器中独立存在的api接口：
WebsocketController DELETE /api/websocket/disconnect
AdsController GET /api/ads/list
Ai-ModelsController GET /api/ai-models/platform-health/{platform}
Ai-ModelsController GET /api/ai-models/platform-stats/{platform}
Ai-ModelsController GET /api/ai-models/platforms-health
App-MonitorController GET /api/app-monitor/alerts
App-MonitorController GET /api/app-monitor/metrics
App-MonitorController GET /api/app-monitor/realtime
CheckController GET /api/check
ImagesController GET /api/images/{id}/status
MusicController GET /api/music/{id}/status
PermissionsController GET /api/permissions/user/{id}
SocialController GET /api/social/{id}/comments
VideosController GET /api/videos/{id}/status
VoiceController GET /api/voice/clone/{id}/status
VoiceController GET /api/voice/custom/{id}/status
VoiceController GET /api/voice/{id}/status
WorkflowsController GET /api/workflows/{id}/execution-status
WorkflowsController GET /api/workflows/{id}/history
WorksController GET /api/works/{id}/share-link
AdsController POST /api/ads/create
Ai-ModelsController POST /api/ai-models/select-platform
AiController POST /api/ai/text/generate
AudioController POST /api/audio/enhance
MusicController POST /api/music/batch-generate
ResourcesController POST /api/resources/batch-generate
SoundsController POST /api/sounds/batch-generate
User-GrowthController POST /api/user-growth/achievements/{id}/complete
User-GrowthController POST /api/user-growth/daily-tasks/{id}/complete
User-GrowthController POST /api/user-growth/goals
VoiceController POST /api/voice/batch-synthesize
VoiceController POST /api/voice/clone
VoiceController POST /api/voice/custom
VoiceController POST /api/voice/preview
VoiceController POST /api/voice/synthesize
WorkflowsController POST /api/workflows/create
WorkflowsController POST /api/workflows/{id}/cancel
WorkflowsController POST /api/workflows/{id}/step-input
App-MonitorController PUT /api/app-monitor/alerts/{id}/acknowledge
App-MonitorController PUT /api/app-monitor/alerts/{id}/resolve
SocialController PUT /api/social/notifications/read

**控制器中独立存在的接口总数：41个**

## 应用规则
- 遵循 @.cursor/rules/ 文件夹内的所有适用规则
- 严格按照用户指定的汇报格式输出
- 使用 Claude Sonnet 4 模型进行精确分析

## 应用知识报告
本次分析应用了以下规则和知识：
- 遵循了 `@.cursor/rules/` 文件夹中的API接口增/删/改铁律
- 使用了系统性的正则表达式匹配进行精确提取
- 应用了集合运算进行差异化对比分析

## 应用模型报告
- 使用模型：Claude Sonnet 4
- 版本：最新版本
- 应用场景：代码分析、API接口对比、文档解析、Python脚本开发

@CogniAud 请对此分析结果进行审计，确认差异化检测的准确性和完整性。
